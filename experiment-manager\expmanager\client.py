"""
实验管理客户端 - 核心功能模块
基于奥卡姆剃刀原则设计的简洁API
"""

import atexit
import webbrowser
import requests
import subprocess
import os
from typing import Dict, Any
from datetime import datetime

from .config import Config
from .exceptions import ExpManagerError, ServerNotAvailableError
from .server import ensure_server_running


class ExperimentManager:
    """实验管理器 - 核心客户端类"""

    def __init__(self, api_base_url: str = None, config: Config = None):
        """
        初始化实验管理器

        Args:
            api_base_url: API服务器地址，默认从配置读取
            config: 配置对象，默认使用全局配置
        """
        self.config = config or Config()
        self.api_base_url = (api_base_url or self.config.api_url).rstrip("/")
        self.session = requests.Session()
        self.session.timeout = self.config.request_timeout

    def register_experiment(
        self,
        name: str,
        hypothesis: str = None,
        description: str = None,
        tags: list = None,
    ) -> str:
        """
        注册新实验

        Args:
            name: 实验名称
            hypothesis: 实验假设
            description: 实验描述
            tags: 实验标签

        Returns:
            实验ID

        Raises:
            ExpManagerError: 实验注册失败
        """
        try:
            # 检查服务器可用性
            self._check_server_health()

            # 收集元数据
            metadata = self._collect_metadata()

            # 准备实验数据
            experiment_data = {
                "name": name,
                "hypothesis": hypothesis,
                "description": description,
                "tags": tags or [],
                "git_hash": metadata.get("git_hash"),
                "environment_info": metadata.get("environment"),
                "created_at": datetime.now().isoformat(),
            }

            # 调用API注册实验
            response = self.session.post(
                f"{self.api_base_url}/api/experiments", json=experiment_data
            )
            response.raise_for_status()

            experiment = response.json()
            experiment_id = experiment["id"]

            if self.config.verbose:
                print(f"实验已注册: {name} (ID: {experiment_id})")

            return experiment_id

        except requests.exceptions.RequestException as e:
            raise ExpManagerError(f"实验注册失败: {e}")
        except Exception as e:
            if self.config.fallback_mode:
                # 降级模式：返回临时ID
                temp_id = f"temp_{int(datetime.now().timestamp())}"
                if self.config.verbose:
                    print(f"警告: 服务器不可用，使用临时ID: {temp_id}")
                return temp_id
            else:
                raise ExpManagerError(f"实验注册失败: {e}")

    def setup_exit_hook(self, experiment_id: str):
        """
        设置退出钩子，实验结束时自动打开浏览器

        Args:
            experiment_id: 实验ID
        """

        def open_review_page():
            try:
                # 更新实验状态为已完成
                self._update_experiment_status(experiment_id, "completed")

                if self.config.auto_open_browser:
                    # 打开复盘页面
                    review_url = (
                        f"{self.config.frontend_url}/experiments/{experiment_id}/review"
                    )
                    if self.config.verbose:
                        print(f"实验完成，正在打开复盘页面: {review_url}")
                    webbrowser.open(review_url)
                else:
                    if self.config.verbose:
                        review_url = (
                            f"{self.config.frontend_url}/experiments/"
                            f"{experiment_id}/review"
                        )
                        print(f"实验完成，复盘页面: {review_url}")

            except Exception as e:
                if self.config.verbose:
                    print(f"警告: 自动复盘失败: {e}")
                    manual_url = (
                        f"{self.config.frontend_url}/experiments/"
                        f"{experiment_id}/review"
                    )
                    print(f"请手动访问: {manual_url}")

        # 注册退出钩子
        atexit.register(open_review_page)

        if self.config.verbose:
            print(f"已设置自动复盘钩子 (实验ID: {experiment_id})")

    def _check_server_health(self):
        """检查服务器健康状态"""
        try:
            response = self.session.get(f"{self.api_base_url}/health", timeout=5)
            response.raise_for_status()
        except Exception:
            raise ServerNotAvailableError(f"服务器不可用: {self.api_base_url}")

    def _collect_metadata(self) -> Dict[str, Any]:
        """收集实验元数据"""
        metadata = {}

        try:
            # 获取Git信息
            git_hash = (
                subprocess.check_output(
                    ["git", "rev-parse", "HEAD"], stderr=subprocess.DEVNULL
                )
                .decode()
                .strip()
            )
            metadata["git_hash"] = git_hash
        except Exception:
            metadata["git_hash"] = None

        try:
            # 获取环境信息
            metadata["environment"] = {
                "python_version": os.sys.version,
                "working_directory": os.getcwd(),
                "user": os.environ.get("USER", os.environ.get("USERNAME", "unknown")),
                "platform": os.name,
            }
        except Exception:
            metadata["environment"] = {}

        return metadata

    def _update_experiment_status(self, experiment_id: str, status: str):
        """更新实验状态"""
        try:
            response = self.session.patch(
                f"{self.api_base_url}/api/experiments/{experiment_id}",
                json={"status": status, "completed_at": datetime.now().isoformat()},
            )
            response.raise_for_status()
        except Exception as e:
            if self.config.verbose:
                print(f"警告: 更新实验状态失败: {e}")


# 便捷函数
def start_experiment(
    name: str,
    hypothesis: str = None,
    description: str = None,
    tags: list = None,
    config: Config = None,
) -> str:
    """
    快速启动实验的便捷函数

    Args:
        name: 实验名称
        hypothesis: 实验假设
        description: 实验描述
        tags: 实验标签
        config: 配置对象

    Returns:
        实验ID

    Example:
        >>> exp_id = start_experiment(
        ...     name="深度学习优化实验",
        ...     hypothesis="Adam优化器将提升收敛速度20%",
        ...     tags=["deep-learning", "optimization"]
        ... )
        >>> print(f"实验ID: {exp_id}")
    """
    # 确保配置对象存在
    if config is None:
        from .config import get_config

        config = get_config()

    # 智能启动服务器 - 真正做到开箱即用
    if config.server_auto_start or config.force_server_start:
        if config.verbose:
            print("[INFO] ExpManager正在为您准备实验环境...")

        # 确保服务器运行
        server_started = ensure_server_running(config)

        if server_started and config.verbose:
            print("[OK] 实验环境准备完成")
        elif config.verbose:
            print("[INFO] 使用离线模式继续")

    manager = ExperimentManager(config=config)
    exp_id = manager.register_experiment(name, hypothesis, description, tags)
    manager.setup_exit_hook(exp_id)

    # 如果服务器启动成功且配置允许，自动打开浏览器
    if config.auto_open_browser and ensure_server_running(config):
        try:
            import webbrowser
            import time

            # 给前端一点时间完全加载
            time.sleep(2)
            webbrowser.open(config.frontend_url)
            if config.verbose:
                print(f"[INFO] 已自动打开实验界面: {config.frontend_url}")
        except Exception as e:
            if config.verbose:
                print(f"[WARN] 无法自动打开浏览器: {e}")

    return exp_id
