"""
命令行接口模块
提供expmanager命令行工具
"""

import argparse
import sys

from . import __version__, get_info
from .config import configure, get_config
from .server import ServerManager
from .client import start_experiment


def main():
    """主命令行入口"""
    parser = argparse.ArgumentParser(
        prog="expmanager",
        description="ExpManager - 科学实验管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  expmanager --version                    # 显示版本信息
  expmanager server start                 # 启动服务器
  expmanager server stop                  # 停止服务器
  expmanager config --api-url http://localhost:8080  # 配置API地址
  expmanager test                         # 运行测试实验
        """,
    )

    parser.add_argument(
        "--version", action="version", version=f"ExpManager {__version__}"
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 服务器管理命令
    server_parser = subparsers.add_parser("server", help="服务器管理")
    server_subparsers = server_parser.add_subparsers(dest="server_action")

    server_subparsers.add_parser("start", help="启动服务器")
    server_subparsers.add_parser("stop", help="停止服务器")
    server_subparsers.add_parser("status", help="查看服务器状态")
    server_subparsers.add_parser("restart", help="重启服务器")

    # 配置命令
    config_parser = subparsers.add_parser("config", help="配置管理")
    config_parser.add_argument("--api-url", help="设置API服务器地址")
    config_parser.add_argument("--frontend-url", help="设置前端地址")
    config_parser.add_argument("--auto-browser", type=bool, help="自动打开浏览器")
    config_parser.add_argument("--verbose-mode", type=bool, help="详细输出模式")
    config_parser.add_argument("--show", action="store_true", help="显示当前配置")
    config_parser.add_argument("--reset", action="store_true", help="重置为默认配置")

    # 测试命令
    test_parser = subparsers.add_parser("test", help="运行测试实验")
    test_parser.add_argument("--name", default="CLI测试实验", help="实验名称")
    test_parser.add_argument("--hypothesis", default="验证CLI功能正常", help="实验假设")

    # 信息命令
    subparsers.add_parser("info", help="显示包信息")

    args = parser.parse_args()

    # 设置详细输出
    if args.verbose:
        configure(verbose=True)

    # 执行命令
    try:
        if args.command == "server":
            handle_server_command(args)
        elif args.command == "config":
            handle_config_command(args)
        elif args.command == "test":
            handle_test_command(args)
        elif args.command == "info":
            handle_info_command()
        else:
            parser.print_help()
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def handle_server_command(args):
    """处理服务器命令"""
    manager = ServerManager()

    if args.server_action == "start":
        print("🚀 启动服务器...")
        if manager.start_server():
            print("✅ 服务器启动成功")
            print(f"   API: {get_config().api_url}")
            print(f"   前端: {get_config().frontend_url}")
        else:
            print("❌ 服务器启动失败")
            sys.exit(1)

    elif args.server_action == "stop":
        print("🛑 停止服务器...")
        manager.stop_server()
        print("✅ 服务器已停止")

    elif args.server_action == "status":
        if manager.is_server_running():
            print("✅ 服务器正在运行")
            print(f"   API: {get_config().api_url}")
        else:
            print("❌ 服务器未运行")

    elif args.server_action == "restart":
        print("🔄 重启服务器...")
        manager.stop_server()
        if manager.start_server():
            print("✅ 服务器重启成功")
        else:
            print("❌ 服务器重启失败")
            sys.exit(1)


def handle_config_command(args):
    """处理配置命令"""
    if args.show:
        config = get_config()
        print("📋 当前配置:")
        for key, value in config.to_dict().items():
            print(f"   {key}: {value}")
        return

    if args.reset:
        from .config import reset_config

        reset_config()
        print("✅ 配置已重置为默认值")
        return

    # 更新配置
    updates = {}
    if args.api_url:
        updates["api_url"] = args.api_url
    if args.frontend_url:
        updates["frontend_url"] = args.frontend_url
    if args.auto_browser is not None:
        updates["auto_open_browser"] = args.auto_browser
    if args.verbose_mode is not None:
        updates["verbose"] = args.verbose_mode

    if updates:
        configure(**updates)
        print("✅ 配置已更新:")
        for key, value in updates.items():
            print(f"   {key}: {value}")

        # 保存配置
        get_config().save_config()
    else:
        print("⚠️ 没有指定要更新的配置项")


def handle_test_command(args):
    """处理测试命令"""
    print("🧪 运行测试实验...")

    exp_id = start_experiment(
        name=args.name, hypothesis=args.hypothesis, tags=["cli-test"]
    )

    print(f"✅ 测试实验已启动 (ID: {exp_id})")
    print("🔗 实验将在脚本结束时自动打开复盘页面")


def handle_info_command():
    """处理信息命令"""
    info = get_info()
    print("📋 ExpManager 包信息:")
    for key, value in info.items():
        print(f"   {key}: {value}")


if __name__ == "__main__":
    main()
