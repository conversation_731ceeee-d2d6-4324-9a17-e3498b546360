"""
ExpManager - 实验管理系统Python客户端库

一个基于"三阶段十二步契约流程"的科学实验管理工具，
将实验从一门"艺术"变成一门"工程"。

核心特性：
- 🧪 实验全生命周期管理
- 📊 智能复盘分析
- 🚀 一键集成，零配置
- 🌐 Web界面可视化
- 📁 标准化数据导出

使用示例：
    from expmanager import start_experiment

    # 只需一行代码启动实验管理
    exp_id = start_experiment(
        name="深度学习优化实验",
        hypothesis="Adam优化器将提升收敛速度20%"
    )

    # 你的实验代码...
    # 脚本结束时自动打开复盘页面

作者: Sean (deepractice.ai)
版本: 1.0.0
许可: MIT
"""

__version__ = "1.0.0"
__author__ = "Sean (deepractice.ai)"
__email__ = "<EMAIL>"
__license__ = "MIT"


# 核心API导入 - 使用延迟导入避免循环依赖
def _import_components():
    """延迟导入组件"""
    try:
        from .client import ExperimentManager, start_experiment
        from .server import ServerManager, ensure_server_running
        from .config import Config, configure
        from .exceptions import ExpManagerError, ServerNotAvailableError

        return {
            "ExperimentManager": ExperimentManager,
            "start_experiment": start_experiment,
            "ServerManager": ServerManager,
            "ensure_server_running": ensure_server_running,
            "Config": Config,
            "configure": configure,
            "ExpManagerError": ExpManagerError,
            "ServerNotAvailableError": ServerNotAvailableError,
        }
    except ImportError as e:
        # 如果导入失败，提供友好的错误信息
        raise ImportError(f"ExpManager组件导入失败: {e}")


# 延迟导入
_components = None


def __getattr__(name):
    """动态属性访问"""
    global _components
    if _components is None:
        _components = _import_components()

    if name in _components:
        return _components[name]

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# 便捷导入
__all__ = [
    # 核心功能
    "start_experiment",
    "ExperimentManager",
    "ServerManager",
    "ensure_server_running",
    # 配置管理
    "Config",
    "configure",
    # 异常类
    "ExpManagerError",
    "ServerNotAvailableError",
    # 元信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
]


def get_version():
    """获取版本信息"""
    return __version__


def get_info():
    """获取包信息"""
    return {
        "name": "expmanager",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "license": __license__,
        "description": "科学实验管理工具 - 将实验从艺术变成工程",
    }
