"""
服务器管理模块
自动启动和管理实验管理服务器
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path
from typing import Optional

from .config import Config
from .exceptions import ServerStartupError


class ServerManager:
    """服务器管理器"""

    def __init__(self, config: Config = None):
        """
        初始化服务器管理器

        Args:
            config: 配置对象
        """
        self.config = config or Config()
        self.backend_process = None
        self.frontend_process = None

    def is_server_running(self) -> bool:
        """检查服务器是否运行"""
        try:
            response = requests.get(f"{self.config.api_url}/health", timeout=10)
            if response.status_code == 200:
                if self.config.verbose:
                    print("[OK] 服务器健康检查通过")
                return True
            else:
                if self.config.verbose:
                    print(f"[WARN] 服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            if self.config.verbose:
                print(f"[WARN] 无法连接到服务器: {self.config.api_url}")
                print("提示: 请确保后端服务正在运行")
            return False
        except requests.exceptions.Timeout:
            if self.config.verbose:
                print("[WARN] 服务器响应超时")
            return False
        except Exception as e:
            if self.config.verbose:
                print(f"[WARN] 服务器检查出错: {e}")
            return False

    def start_server(self, force_restart: bool = False) -> bool:
        """
        启动服务器

        Args:
            force_restart: 是否强制重启

        Returns:
            是否启动成功
        """
        if not force_restart and self.is_server_running():
            if self.config.verbose:
                print("服务器已在运行")
            return True

        try:
            # 查找服务器文件
            server_path = self._find_server_path()
            if not server_path:
                raise ServerStartupError("未找到服务器文件")

            if self.config.verbose:
                print(f"[INFO] 找到服务器路径: {server_path}")

            # 检查并安装依赖
            if self.config.auto_install_dependencies:
                self._ensure_dependencies(server_path)

            # 启动后端
            if self.config.verbose:
                print("[INFO] 启动后端服务...")
            self._start_backend(server_path)

            # 启动前端
            if self.config.verbose:
                print("[INFO] 启动前端服务...")
            self._start_frontend(server_path)

            # 等待服务器启动
            if self.config.verbose:
                print("[INFO] 等待服务器完全启动...")
            if self._wait_for_server():
                if self.config.verbose:
                    print("[OK] 服务器启动成功")
                    print(f"[INFO] 后端API: {self.config.api_url}")
                    print(f"[INFO] 前端界面: {self.config.frontend_url}")
                return True
            else:
                raise ServerStartupError("服务器启动超时")

        except Exception as e:
            if self.config.verbose:
                print(f"服务器启动失败: {e}")
            return False

    def _get_npm_command(self):
        """获取npm命令，处理Windows路径问题"""
        npm_commands = ["npm", "npm.cmd", "npm.exe"]

        for cmd in npm_commands:
            try:
                result = subprocess.run(
                    [cmd, "--version"], capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    if self.config.verbose:
                        print(f"[OK] 找到npm: {cmd} (版本: {result.stdout.strip()})")
                    return cmd
            except (FileNotFoundError, subprocess.TimeoutExpired):
                continue

        # 尝试通过where命令查找npm
        if sys.platform == "win32":
            try:
                result = subprocess.run(
                    ["where", "npm"], capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    npm_path = result.stdout.strip().split("\n")[0]
                    if self.config.verbose:
                        print(f"[OK] 通过where找到npm: {npm_path}")
                    return npm_path
            except Exception:
                pass

        return None

    def _ensure_port_available(self, port: int):
        """确保端口可用，如果被占用则尝试释放"""
        try:
            import socket

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(("localhost", port))
            sock.close()

            if result == 0:  # 端口被占用
                if self.config.verbose:
                    print(f"[WARN] 端口 {port} 被占用，尝试释放...")

                # 在Windows上尝试杀掉占用端口的进程
                if sys.platform == "win32":
                    try:
                        # 查找占用端口的进程
                        result = subprocess.run(
                            ["netstat", "-ano"], capture_output=True, text=True
                        )

                        for line in result.stdout.split("\n"):
                            if f":{port}" in line and "LISTENING" in line:
                                parts = line.split()
                                if len(parts) >= 5:
                                    pid = parts[-1]
                                    if pid.isdigit():
                                        subprocess.run(
                                            ["taskkill", "/PID", pid, "/F"],
                                            capture_output=True,
                                        )
                                        if self.config.verbose:
                                            print(f"[OK] 已释放端口 {port}")
                                        break
                    except Exception as e:
                        if self.config.verbose:
                            print(f"[WARN] 无法释放端口 {port}: {e}")
        except Exception as e:
            if self.config.verbose:
                print(f"[WARN] 端口检查失败: {e}")

    def _ensure_dependencies(self, server_path: Path):
        """确保依赖已安装"""
        frontend_path = server_path / "frontend"

        if not frontend_path.exists():
            return

        node_modules_path = frontend_path / "node_modules"
        package_json_path = frontend_path / "package.json"

        # 检查是否需要安装依赖
        if package_json_path.exists() and not node_modules_path.exists():
            if self.config.verbose:
                print("[INFO] 检测到前端依赖未安装，正在自动安装...")

            try:
                # 检查npm是否可用
                subprocess.check_call(
                    ["npm", "--version"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                )

                # 安装依赖
                result = subprocess.run(
                    ["npm", "install"],
                    cwd=frontend_path,
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5分钟超时
                )

                if result.returncode == 0:
                    if self.config.verbose:
                        print("[OK] 前端依赖安装成功")
                else:
                    if self.config.verbose:
                        print(f"[WARN] 前端依赖安装失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                if self.config.verbose:
                    print("[WARN] 前端依赖安装超时")
            except Exception as e:
                if self.config.verbose:
                    print(f"[WARN] 前端依赖安装出错: {e}")

    def stop_server(self):
        """停止服务器"""
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=10)
            except Exception:
                self.backend_process.kill()
            self.backend_process = None

        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=10)
            except Exception:
                self.frontend_process.kill()
            self.frontend_process = None

        if self.config.verbose:
            print("服务器已停止")

    def _find_server_path(self) -> Optional[Path]:
        """查找服务器路径"""
        # 可能的服务器位置
        possible_paths = [
            Path(__file__).parent.parent,  # 包内路径
            Path.cwd(),  # 当前目录
            Path.cwd() / "experiment-manager",  # 子目录
            Path.home() / ".expmanager",  # 用户目录
        ]

        for path in possible_paths:
            if (path / "backend" / "main.py").exists():
                return path

        return None

    def _start_backend(self, server_path: Path):
        """启动后端服务"""
        backend_path = server_path / "backend"

        # 检查依赖
        requirements_file = backend_path / "requirements.txt"
        if requirements_file.exists():
            try:
                subprocess.check_call(
                    [
                        sys.executable,
                        "-m",
                        "pip",
                        "install",
                        "-r",
                        str(requirements_file),
                    ],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                )
            except Exception:
                pass  # 忽略依赖安装失败

        # 启动后端
        cmd = [
            sys.executable,
            "-m",
            "uvicorn",
            "main:app",
            "--host",
            "0.0.0.0",
            "--port",
            "8000",
        ]

        # 启动独立的后端进程
        if sys.platform == "win32":
            # Windows: 创建新的进程组
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=backend_path,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
            )
        else:
            # Unix/Linux: 创建新的会话
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=backend_path,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True,
            )

        if self.config.verbose:
            print("后端服务启动中...")

    def _start_frontend(self, server_path: Path):
        """启动前端服务"""
        frontend_path = server_path / "frontend"

        if not frontend_path.exists():
            if self.config.verbose:
                print("[INFO] 前端目录不存在，跳过前端启动")
            return  # 前端可选

        # 检查Node.js是否可用
        try:
            subprocess.check_call(
                ["node", "--version"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
            )
        except Exception:
            if self.config.verbose:
                print("[WARN] Node.js未安装，无法启动前端服务")
                print("提示: 请安装Node.js或手动运行 start_frontend.bat")
            return

        # 检查npm是否可用 - 改进的Windows兼容版本
        npm_cmd = self._get_npm_command()
        if not npm_cmd:
            if self.config.verbose:
                print("[WARN] npm未找到，无法启动前端服务")
                print("提示: 请确保Node.js已安装并在PATH中")
            return

        # 检查Node.js依赖
        if (frontend_path / "package.json").exists():
            try:
                if self.config.verbose:
                    print("检查前端依赖...")

                # 检查node_modules是否存在
                if not (frontend_path / "node_modules").exists():
                    if self.config.verbose:
                        print("安装前端依赖...")
                    subprocess.check_call(
                        [npm_cmd, "install"],
                        cwd=frontend_path,
                        stdout=subprocess.DEVNULL if not self.config.verbose else None,
                        stderr=subprocess.DEVNULL if not self.config.verbose else None,
                    )
                    if self.config.verbose:
                        print("[OK] 前端依赖安装成功")

                # 启动前端
                if self.config.verbose:
                    print("[INFO] 启动前端服务...")

                # 确保3000端口可用
                self._ensure_port_available(3000)

                # Windows上使用批处理文件启动前端
                if sys.platform == "win32":
                    # 优先使用简单的批处理文件
                    simple_batch = server_path / "start_frontend_simple.bat"
                    if simple_batch.exists():
                        if self.config.verbose:
                            print(f"[INFO] 使用批处理文件启动: {simple_batch}")
                        self.frontend_process = subprocess.Popen(
                            [str(simple_batch)],
                            cwd=server_path,
                            stdout=(
                                subprocess.DEVNULL if not self.config.verbose else None
                            ),
                            stderr=(
                                subprocess.DEVNULL if not self.config.verbose else None
                            ),
                            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                        )
                    else:
                        # 回退到复杂的批处理文件
                        batch_file = server_path / "start_frontend.bat"
                        if batch_file.exists():
                            self.frontend_process = subprocess.Popen(
                                [str(batch_file)],
                                cwd=server_path,
                                stdout=(
                                    subprocess.DEVNULL
                                    if not self.config.verbose
                                    else None
                                ),
                                stderr=(
                                    subprocess.DEVNULL
                                    if not self.config.verbose
                                    else None
                                ),
                                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                            )
                        else:
                            # 最后回退到直接命令
                            env = os.environ.copy()
                            env["NEXT_PUBLIC_API_URL"] = self.config.api_url

                            self.frontend_process = subprocess.Popen(
                                ["cmd", "/c", f"{npm_cmd} run dev"],
                                cwd=frontend_path,
                                stdout=(
                                    subprocess.DEVNULL
                                    if not self.config.verbose
                                    else None
                                ),
                                stderr=(
                                    subprocess.DEVNULL
                                    if not self.config.verbose
                                    else None
                                ),
                                env=env,
                                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                            )
                else:
                    # Unix/Linux系统
                    self.frontend_process = subprocess.Popen(
                        [npm_cmd, "run", "dev"],
                        cwd=frontend_path,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        start_new_session=True,
                    )

                if self.config.verbose:
                    print("前端服务启动中... (http://localhost:3000)")

            except Exception as e:
                if self.config.verbose:
                    print(f"[ERROR] 前端启动失败: {e}")
                    print("提示: 请手动运行 start_frontend.bat")
                    print("或手动启动: cd frontend && npm install && npm run dev")

    def _wait_for_server(self) -> bool:
        """等待服务器启动"""
        start_time = time.time()

        while time.time() - start_time < self.config.server_startup_timeout:
            if self.is_server_running():
                return True
            time.sleep(1)

        return False

    def __del__(self):
        """析构函数，不自动清理进程，让服务器持续运行"""
        # self.stop_server()  # 注释掉自动清理
        pass


# 全局服务器管理器
_server_manager = None


def ensure_server_running(config: Config = None) -> bool:
    """
    确保服务器运行 - 增强版，真正做到开箱即用

    Args:
        config: 配置对象

    Returns:
        是否成功启动
    """
    global _server_manager

    if config is None:
        from .config import get_config

        config = get_config()

    if _server_manager is None:
        _server_manager = ServerManager(config)

    # 首先检查服务器是否已经运行
    if _server_manager.is_server_running():
        if config.verbose:
            print("[OK] 服务器已在运行")
        return True

    # 如果服务器未运行，进行智能启动
    if config.server_auto_start or config.force_server_start:
        if config.verbose:
            print("[INFO] 检测到服务器未运行，正在自动启动...")

        # 多次尝试启动
        for attempt in range(config.max_startup_attempts):
            if config.verbose and attempt > 0:
                print(f"[INFO] 第 {attempt + 1} 次启动尝试...")

            success = _server_manager.start_server()

            if success and config.wait_for_server:
                # 等待服务器完全启动
                if config.verbose:
                    print("[INFO] 等待服务器完全启动...")

                import time

                for check_attempt in range(
                    config.server_startup_timeout // config.startup_check_interval
                ):
                    time.sleep(config.startup_check_interval)
                    if _server_manager.is_server_running():
                        if config.verbose:
                            print("[OK] 服务器启动成功并已就绪")
                        return True
                    if config.verbose:
                        wait_time = (check_attempt + 1) * config.startup_check_interval
                        print(f"[INFO] 等待中... ({wait_time}s)")
            elif success:
                if config.verbose:
                    print("[OK] 服务器启动成功")
                return True

            if attempt < config.max_startup_attempts - 1:
                if config.verbose:
                    print(
                        f"[WARN] 启动失败，{config.startup_check_interval}秒后重试..."
                    )
                import time

                time.sleep(config.startup_check_interval)

        # 所有尝试都失败了
        if config.verbose:
            print("[ERROR] 服务器启动失败，已尝试所有方法")
            if config.fallback_mode:
                print("[INFO] 将使用降级模式继续运行")
        return False
    else:
        if config.verbose:
            print("[WARN] 服务器未运行且自动启动已禁用")
            if config.fallback_mode:
                print("[INFO] 将使用降级模式继续运行")
        return False


def stop_global_server():
    """停止全局服务器"""
    global _server_manager
    if _server_manager:
        _server_manager.stop_server()
        _server_manager = None


# 注册退出清理 - 注释掉自动清理，让服务器持续运行
# import atexit
# atexit.register(stop_global_server)
