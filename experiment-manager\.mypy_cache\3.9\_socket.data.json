{".class": "MypyFile", "_fullname": "_socket", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AF_ALG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ALG", "name": "AF_ALG", "setter_type": null, "type": "builtins.int"}}, "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_APPLETALK", "name": "AF_APPLETALK", "setter_type": null, "type": "builtins.int"}}, "AF_ASH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ASH", "name": "AF_ASH", "setter_type": null, "type": "builtins.int"}}, "AF_ATMPVC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ATMPVC", "name": "AF_ATMPVC", "setter_type": null, "type": "builtins.int"}}, "AF_ATMSVC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ATMSVC", "name": "AF_ATMSVC", "setter_type": null, "type": "builtins.int"}}, "AF_AX25": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_AX25", "name": "AF_AX25", "setter_type": null, "type": "builtins.int"}}, "AF_BRIDGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_BRIDGE", "name": "AF_BRIDGE", "setter_type": null, "type": "builtins.int"}}, "AF_CAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_CAN", "name": "AF_CAN", "setter_type": null, "type": "builtins.int"}}, "AF_DECnet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_DECnet", "name": "AF_DECnet", "setter_type": null, "type": "builtins.int"}}, "AF_ECONET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ECONET", "name": "AF_ECONET", "setter_type": null, "type": "builtins.int"}}, "AF_INET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_INET", "name": "AF_INET", "setter_type": null, "type": "builtins.int"}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_INET6", "name": "AF_INET6", "setter_type": null, "type": "builtins.int"}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_IPX", "name": "AF_IPX", "setter_type": null, "type": "builtins.int"}}, "AF_IRDA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_IRDA", "name": "AF_IRDA", "setter_type": null, "type": "builtins.int"}}, "AF_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_KEY", "name": "AF_KEY", "setter_type": null, "type": "builtins.int"}}, "AF_LLC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_LLC", "name": "AF_LLC", "setter_type": null, "type": "builtins.int"}}, "AF_NETBEUI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_NETBEUI", "name": "AF_NETBEUI", "setter_type": null, "type": "builtins.int"}}, "AF_NETLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_NETLINK", "name": "AF_NETLINK", "setter_type": null, "type": "builtins.int"}}, "AF_NETROM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_NETROM", "name": "AF_NETROM", "setter_type": null, "type": "builtins.int"}}, "AF_PACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_PACKET", "name": "AF_PACKET", "setter_type": null, "type": "builtins.int"}}, "AF_PPPOX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_PPPOX", "name": "AF_PPPOX", "setter_type": null, "type": "builtins.int"}}, "AF_QIPCRTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_QIPCRTR", "name": "AF_QIPCRTR", "setter_type": null, "type": "builtins.int"}}, "AF_RDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_RDS", "name": "AF_RDS", "setter_type": null, "type": "builtins.int"}}, "AF_ROSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ROSE", "name": "AF_ROSE", "setter_type": null, "type": "builtins.int"}}, "AF_ROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_ROUTE", "name": "AF_ROUTE", "setter_type": null, "type": "builtins.int"}}, "AF_SECURITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_SECURITY", "name": "AF_SECURITY", "setter_type": null, "type": "builtins.int"}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_SNA", "name": "AF_SNA", "setter_type": null, "type": "builtins.int"}}, "AF_TIPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_TIPC", "name": "AF_TIPC", "setter_type": null, "type": "builtins.int"}}, "AF_UNIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_UNIX", "name": "AF_UNIX", "setter_type": null, "type": "builtins.int"}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_UNSPEC", "name": "AF_UNSPEC", "setter_type": null, "type": "builtins.int"}}, "AF_VSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_VSOCK", "name": "AF_VSOCK", "setter_type": null, "type": "builtins.int"}}, "AF_WANPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_WANPIPE", "name": "AF_WANPIPE", "setter_type": null, "type": "builtins.int"}}, "AF_X25": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AF_X25", "name": "AF_X25", "setter_type": null, "type": "builtins.int"}}, "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "setter_type": null, "type": "builtins.int"}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_ALL", "name": "AI_ALL", "setter_type": null, "type": "builtins.int"}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_CANONNAME", "name": "AI_CANONNAME", "setter_type": null, "type": "builtins.int"}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "setter_type": null, "type": "builtins.int"}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "setter_type": null, "type": "builtins.int"}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_PASSIVE", "name": "AI_PASSIVE", "setter_type": null, "type": "builtins.int"}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.AI_V4MAPPED", "name": "AI_V4MAPPED", "setter_type": null, "type": "builtins.int"}}, "ALG_OP_DECRYPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_OP_DECRYPT", "name": "ALG_OP_DECRYPT", "setter_type": null, "type": "builtins.int"}}, "ALG_OP_ENCRYPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_OP_ENCRYPT", "name": "ALG_OP_ENCRYPT", "setter_type": null, "type": "builtins.int"}}, "ALG_OP_SIGN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_OP_SIGN", "name": "ALG_OP_SIGN", "setter_type": null, "type": "builtins.int"}}, "ALG_OP_VERIFY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_OP_VERIFY", "name": "ALG_OP_VERIFY", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_AEAD_ASSOCLEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_AEAD_ASSOCLEN", "name": "ALG_SET_AEAD_ASSOCLEN", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_AEAD_AUTHSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_AEAD_AUTHSIZE", "name": "ALG_SET_AEAD_AUTHSIZE", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_IV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_IV", "name": "ALG_SET_IV", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_KEY", "name": "ALG_SET_KEY", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_OP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_OP", "name": "ALG_SET_OP", "setter_type": null, "type": "builtins.int"}}, "ALG_SET_PUBKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.ALG_SET_PUBKEY", "name": "ALG_SET_PUBKEY", "setter_type": null, "type": "builtins.int"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CAN_BCM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM", "name": "CAN_BCM", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_CAN_FD_FRAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_CAN_FD_FRAME", "name": "CAN_BCM_CAN_FD_FRAME", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_ANNOUNCE_RESUME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_ANNOUNCE_RESUME", "name": "CAN_BCM_RX_ANNOUNCE_RESUME", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_CHANGED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_CHANGED", "name": "CAN_BCM_RX_CHANGED", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_CHECK_DLC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_CHECK_DLC", "name": "CAN_BCM_RX_CHECK_DLC", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_DELETE", "name": "CAN_BCM_RX_DELETE", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_FILTER_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_FILTER_ID", "name": "CAN_BCM_RX_FILTER_ID", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_NO_AUTOTIMER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_NO_AUTOTIMER", "name": "CAN_BCM_RX_NO_AUTOTIMER", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_READ", "name": "CAN_BCM_RX_READ", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_RTR_FRAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_RTR_FRAME", "name": "CAN_BCM_RX_RTR_FRAME", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_SETUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_SETUP", "name": "CAN_BCM_RX_SETUP", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_STATUS", "name": "CAN_BCM_RX_STATUS", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_RX_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_RX_TIMEOUT", "name": "CAN_BCM_RX_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_SETTIMER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_SETTIMER", "name": "CAN_BCM_SETTIMER", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_STARTTIMER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_STARTTIMER", "name": "CAN_BCM_STARTTIMER", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_ANNOUNCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_ANNOUNCE", "name": "CAN_BCM_TX_ANNOUNCE", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_COUNTEVT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_COUNTEVT", "name": "CAN_BCM_TX_COUNTEVT", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_CP_CAN_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_CP_CAN_ID", "name": "CAN_BCM_TX_CP_CAN_ID", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_DELETE", "name": "CAN_BCM_TX_DELETE", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_EXPIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_EXPIRED", "name": "CAN_BCM_TX_EXPIRED", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_READ", "name": "CAN_BCM_TX_READ", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_RESET_MULTI_IDX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_RESET_MULTI_IDX", "name": "CAN_BCM_TX_RESET_MULTI_IDX", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_SEND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_SEND", "name": "CAN_BCM_TX_SEND", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_SETUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_SETUP", "name": "CAN_BCM_TX_SETUP", "setter_type": null, "type": "builtins.int"}}, "CAN_BCM_TX_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_BCM_TX_STATUS", "name": "CAN_BCM_TX_STATUS", "setter_type": null, "type": "builtins.int"}}, "CAN_EFF_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_EFF_FLAG", "name": "CAN_EFF_FLAG", "setter_type": null, "type": "builtins.int"}}, "CAN_EFF_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_EFF_MASK", "name": "CAN_EFF_MASK", "setter_type": null, "type": "builtins.int"}}, "CAN_ERR_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_ERR_FLAG", "name": "CAN_ERR_FLAG", "setter_type": null, "type": "builtins.int"}}, "CAN_ERR_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_ERR_MASK", "name": "CAN_ERR_MASK", "setter_type": null, "type": "builtins.int"}}, "CAN_ISOTP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_ISOTP", "name": "CAN_ISOTP", "setter_type": null, "type": "builtins.int"}}, "CAN_J1939": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_J1939", "name": "CAN_J1939", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW", "name": "CAN_RAW", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_ERR_FILTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_ERR_FILTER", "name": "CAN_RAW_ERR_FILTER", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_FD_FRAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_FD_FRAMES", "name": "CAN_RAW_FD_FRAMES", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_FILTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_FILTER", "name": "CAN_RAW_FILTER", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_JOIN_FILTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_JOIN_FILTERS", "name": "CAN_RAW_JOIN_FILTERS", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_LOOPBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_LOOPBACK", "name": "CAN_RAW_LOOPBACK", "setter_type": null, "type": "builtins.int"}}, "CAN_RAW_RECV_OWN_MSGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RAW_RECV_OWN_MSGS", "name": "CAN_RAW_RECV_OWN_MSGS", "setter_type": null, "type": "builtins.int"}}, "CAN_RTR_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_RTR_FLAG", "name": "CAN_RTR_FLAG", "setter_type": null, "type": "builtins.int"}}, "CAN_SFF_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAN_SFF_MASK", "name": "CAN_SFF_MASK", "setter_type": null, "type": "builtins.int"}}, "CAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.CAPI", "name": "CAPI", "setter_type": null, "type": "typing_extensions.CapsuleType"}}, "CMSG_LEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.CMSG_LEN", "name": "CMSG_LEN", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "CMSG_LEN", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CMSG_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.CMSG_SPACE", "name": "CMSG_SPACE", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "CMSG_SPACE", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CapsuleType": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.CapsuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EAI_ADDRFAMILY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_ADDRFAMILY", "name": "EAI_ADDRFAMILY", "setter_type": null, "type": "builtins.int"}}, "EAI_AGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_AGAIN", "name": "EAI_AGAIN", "setter_type": null, "type": "builtins.int"}}, "EAI_BADFLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_BADFLAGS", "name": "EAI_BADFLAGS", "setter_type": null, "type": "builtins.int"}}, "EAI_FAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_FAIL", "name": "EAI_FAIL", "setter_type": null, "type": "builtins.int"}}, "EAI_FAMILY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_FAMILY", "name": "EAI_FAMILY", "setter_type": null, "type": "builtins.int"}}, "EAI_MEMORY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_MEMORY", "name": "EAI_MEMORY", "setter_type": null, "type": "builtins.int"}}, "EAI_NODATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_NODATA", "name": "EAI_NODATA", "setter_type": null, "type": "builtins.int"}}, "EAI_NONAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_NONAME", "name": "EAI_NONAME", "setter_type": null, "type": "builtins.int"}}, "EAI_OVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_OVERFLOW", "name": "EAI_OVERFLOW", "setter_type": null, "type": "builtins.int"}}, "EAI_SERVICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_SERVICE", "name": "EAI_SERVICE", "setter_type": null, "type": "builtins.int"}}, "EAI_SOCKTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_SOCKTYPE", "name": "EAI_SOCKTYPE", "setter_type": null, "type": "builtins.int"}}, "EAI_SYSTEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.EAI_SYSTEM", "name": "EAI_SYSTEM", "setter_type": null, "type": "builtins.int"}}, "INADDR_ALLHOSTS_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_ALLHOSTS_GROUP", "name": "INADDR_ALLHOSTS_GROUP", "setter_type": null, "type": "builtins.int"}}, "INADDR_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_ANY", "name": "INADDR_ANY", "setter_type": null, "type": "builtins.int"}}, "INADDR_BROADCAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_BROADCAST", "name": "INADDR_BROADCAST", "setter_type": null, "type": "builtins.int"}}, "INADDR_LOOPBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_LOOPBACK", "name": "INADDR_LOOPBACK", "setter_type": null, "type": "builtins.int"}}, "INADDR_MAX_LOCAL_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_MAX_LOCAL_GROUP", "name": "INADDR_MAX_LOCAL_GROUP", "setter_type": null, "type": "builtins.int"}}, "INADDR_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_NONE", "name": "INADDR_NONE", "setter_type": null, "type": "builtins.int"}}, "INADDR_UNSPEC_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.INADDR_UNSPEC_GROUP", "name": "INADDR_UNSPEC_GROUP", "setter_type": null, "type": "builtins.int"}}, "IOCTL_VM_SOCKETS_GET_LOCAL_CID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IOCTL_VM_SOCKETS_GET_LOCAL_CID", "name": "IOCTL_VM_SOCKETS_GET_LOCAL_CID", "setter_type": null, "type": "builtins.int"}}, "IPPORT_RESERVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPORT_RESERVED", "name": "IPPORT_RESERVED", "setter_type": null, "type": "builtins.int"}}, "IPPORT_USERRESERVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPORT_USERRESERVED", "name": "IPPORT_USERRESERVED", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_AH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_AH", "name": "IPPROTO_AH", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_DSTOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_DSTOPTS", "name": "IPPROTO_DSTOPTS", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_EGP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_EGP", "name": "IPPROTO_EGP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_ESP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_ESP", "name": "IPPROTO_ESP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_FRAGMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_FRAGMENT", "name": "IPPROTO_FRAGMENT", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_GRE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_GRE", "name": "IPPROTO_GRE", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_HOPOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_HOPOPTS", "name": "IPPROTO_HOPOPTS", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_ICMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_ICMP", "name": "IPPROTO_ICMP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_ICMPV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_ICMPV6", "name": "IPPROTO_ICMPV6", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_IDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_IDP", "name": "IPPROTO_IDP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_IGMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_IGMP", "name": "IPPROTO_IGMP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_IP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_IP", "name": "IPPROTO_IP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_IPIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_IPIP", "name": "IPPROTO_IPIP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_IPV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_IPV6", "name": "IPPROTO_IPV6", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_NONE", "name": "IPPROTO_NONE", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_PIM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_PIM", "name": "IPPROTO_PIM", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_PUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_PUP", "name": "IPPROTO_PUP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_RAW", "name": "IPPROTO_RAW", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_ROUTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_ROUTING", "name": "IPPROTO_ROUTING", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_RSVP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_RSVP", "name": "IPPROTO_RSVP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_SCTP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_SCTP", "name": "IPPROTO_SCTP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_TCP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_TCP", "name": "IPPROTO_TCP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_TP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_TP", "name": "IPPROTO_TP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_UDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_UDP", "name": "IPPROTO_UDP", "setter_type": null, "type": "builtins.int"}}, "IPPROTO_UDPLITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPPROTO_UDPLITE", "name": "IPPROTO_UDPLITE", "setter_type": null, "type": "builtins.int"}}, "IPV6_CHECKSUM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_CHECKSUM", "name": "IPV6_CHECKSUM", "setter_type": null, "type": "builtins.int"}}, "IPV6_DONTFRAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_DONTFRAG", "name": "IPV6_DONTFRAG", "setter_type": null, "type": "builtins.int"}}, "IPV6_DSTOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_DSTOPTS", "name": "IPV6_DSTOPTS", "setter_type": null, "type": "builtins.int"}}, "IPV6_HOPLIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_HOPLIMIT", "name": "IPV6_HOPLIMIT", "setter_type": null, "type": "builtins.int"}}, "IPV6_HOPOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_HOPOPTS", "name": "IPV6_HOPOPTS", "setter_type": null, "type": "builtins.int"}}, "IPV6_JOIN_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_JOIN_GROUP", "name": "IPV6_JOIN_GROUP", "setter_type": null, "type": "builtins.int"}}, "IPV6_LEAVE_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_LEAVE_GROUP", "name": "IPV6_LEAVE_GROUP", "setter_type": null, "type": "builtins.int"}}, "IPV6_MULTICAST_HOPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_MULTICAST_HOPS", "name": "IPV6_MULTICAST_HOPS", "setter_type": null, "type": "builtins.int"}}, "IPV6_MULTICAST_IF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_MULTICAST_IF", "name": "IPV6_MULTICAST_IF", "setter_type": null, "type": "builtins.int"}}, "IPV6_MULTICAST_LOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_MULTICAST_LOOP", "name": "IPV6_MULTICAST_LOOP", "setter_type": null, "type": "builtins.int"}}, "IPV6_NEXTHOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_NEXTHOP", "name": "IPV6_NEXTHOP", "setter_type": null, "type": "builtins.int"}}, "IPV6_PATHMTU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_PATHMTU", "name": "IPV6_PATHMTU", "setter_type": null, "type": "builtins.int"}}, "IPV6_PKTINFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_PKTINFO", "name": "IPV6_PKTINFO", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVDSTOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVDSTOPTS", "name": "IPV6_RECVDSTOPTS", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVHOPLIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVHOPLIMIT", "name": "IPV6_RECVHOPLIMIT", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVHOPOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVHOPOPTS", "name": "IPV6_RECVHOPOPTS", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVPATHMTU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVPATHMTU", "name": "IPV6_RECVPATHMTU", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVPKTINFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVPKTINFO", "name": "IPV6_RECVPKTINFO", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVRTHDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVRTHDR", "name": "IPV6_RECVRTHDR", "setter_type": null, "type": "builtins.int"}}, "IPV6_RECVTCLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RECVTCLASS", "name": "IPV6_RECVTCLASS", "setter_type": null, "type": "builtins.int"}}, "IPV6_RTHDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RTHDR", "name": "IPV6_RTHDR", "setter_type": null, "type": "builtins.int"}}, "IPV6_RTHDRDSTOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RTHDRDSTOPTS", "name": "IPV6_RTHDRDSTOPTS", "setter_type": null, "type": "builtins.int"}}, "IPV6_RTHDR_TYPE_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_RTHDR_TYPE_0", "name": "IPV6_RTHDR_TYPE_0", "setter_type": null, "type": "builtins.int"}}, "IPV6_TCLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_TCLASS", "name": "IPV6_TCLASS", "setter_type": null, "type": "builtins.int"}}, "IPV6_UNICAST_HOPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_UNICAST_HOPS", "name": "IPV6_UNICAST_HOPS", "setter_type": null, "type": "builtins.int"}}, "IPV6_V6ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPV6_V6ONLY", "name": "IPV6_V6ONLY", "setter_type": null, "type": "builtins.int"}}, "IPX_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IPX_TYPE", "name": "IPX_TYPE", "setter_type": null, "type": "builtins.int"}}, "IP_ADD_MEMBERSHIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_ADD_MEMBERSHIP", "name": "IP_ADD_MEMBERSHIP", "setter_type": null, "type": "builtins.int"}}, "IP_DEFAULT_MULTICAST_LOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_DEFAULT_MULTICAST_LOOP", "name": "IP_DEFAULT_MULTICAST_LOOP", "setter_type": null, "type": "builtins.int"}}, "IP_DEFAULT_MULTICAST_TTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_DEFAULT_MULTICAST_TTL", "name": "IP_DEFAULT_MULTICAST_TTL", "setter_type": null, "type": "builtins.int"}}, "IP_DROP_MEMBERSHIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_DROP_MEMBERSHIP", "name": "IP_DROP_MEMBERSHIP", "setter_type": null, "type": "builtins.int"}}, "IP_HDRINCL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_HDRINCL", "name": "IP_HDRINCL", "setter_type": null, "type": "builtins.int"}}, "IP_MAX_MEMBERSHIPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_MAX_MEMBERSHIPS", "name": "IP_MAX_MEMBERSHIPS", "setter_type": null, "type": "builtins.int"}}, "IP_MULTICAST_IF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_MULTICAST_IF", "name": "IP_MULTICAST_IF", "setter_type": null, "type": "builtins.int"}}, "IP_MULTICAST_LOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_MULTICAST_LOOP", "name": "IP_MULTICAST_LOOP", "setter_type": null, "type": "builtins.int"}}, "IP_MULTICAST_TTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_MULTICAST_TTL", "name": "IP_MULTICAST_TTL", "setter_type": null, "type": "builtins.int"}}, "IP_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_OPTIONS", "name": "IP_OPTIONS", "setter_type": null, "type": "builtins.int"}}, "IP_RECVOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_RECVOPTS", "name": "IP_RECVOPTS", "setter_type": null, "type": "builtins.int"}}, "IP_RECVRETOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_RECVRETOPTS", "name": "IP_RECVRETOPTS", "setter_type": null, "type": "builtins.int"}}, "IP_RETOPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_RETOPTS", "name": "IP_RETOPTS", "setter_type": null, "type": "builtins.int"}}, "IP_TOS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_TOS", "name": "IP_TOS", "setter_type": null, "type": "builtins.int"}}, "IP_TRANSPARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_TRANSPARENT", "name": "IP_TRANSPARENT", "setter_type": null, "type": "builtins.int"}}, "IP_TTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.IP_TTL", "name": "IP_TTL", "setter_type": null, "type": "builtins.int"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "J1939_EE_INFO_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_EE_INFO_NONE", "name": "J1939_EE_INFO_NONE", "setter_type": null, "type": "builtins.int"}}, "J1939_EE_INFO_TX_ABORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_EE_INFO_TX_ABORT", "name": "J1939_EE_INFO_TX_ABORT", "setter_type": null, "type": "builtins.int"}}, "J1939_FILTER_MAX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_FILTER_MAX", "name": "J1939_FILTER_MAX", "setter_type": null, "type": "builtins.int"}}, "J1939_IDLE_ADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_IDLE_ADDR", "name": "J1939_IDLE_ADDR", "setter_type": null, "type": "builtins.int"}}, "J1939_MAX_UNICAST_ADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_MAX_UNICAST_ADDR", "name": "J1939_MAX_UNICAST_ADDR", "setter_type": null, "type": "builtins.int"}}, "J1939_NLA_BYTES_ACKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_NLA_BYTES_ACKED", "name": "J1939_NLA_BYTES_ACKED", "setter_type": null, "type": "builtins.int"}}, "J1939_NLA_PAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_NLA_PAD", "name": "J1939_NLA_PAD", "setter_type": null, "type": "builtins.int"}}, "J1939_NO_ADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_NO_ADDR", "name": "J1939_NO_ADDR", "setter_type": null, "type": "builtins.int"}}, "J1939_NO_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_NO_NAME", "name": "J1939_NO_NAME", "setter_type": null, "type": "builtins.int"}}, "J1939_NO_PGN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_NO_PGN", "name": "J1939_NO_PGN", "setter_type": null, "type": "builtins.int"}}, "J1939_PGN_ADDRESS_CLAIMED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_PGN_ADDRESS_CLAIMED", "name": "J1939_PGN_ADDRESS_CLAIMED", "setter_type": null, "type": "builtins.int"}}, "J1939_PGN_ADDRESS_COMMANDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_PGN_ADDRESS_COMMANDED", "name": "J1939_PGN_ADDRESS_COMMANDED", "setter_type": null, "type": "builtins.int"}}, "J1939_PGN_MAX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_PGN_MAX", "name": "J1939_PGN_MAX", "setter_type": null, "type": "builtins.int"}}, "J1939_PGN_PDU1_MAX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_PGN_PDU1_MAX", "name": "J1939_PGN_PDU1_MAX", "setter_type": null, "type": "builtins.int"}}, "J1939_PGN_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.J1939_PGN_REQUEST", "name": "J1939_PGN_REQUEST", "setter_type": null, "type": "builtins.int"}}, "MSG_CMSG_CLOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_CMSG_CLOEXEC", "name": "MSG_CMSG_CLOEXEC", "setter_type": null, "type": "builtins.int"}}, "MSG_CONFIRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_CONFIRM", "name": "MSG_CONFIRM", "setter_type": null, "type": "builtins.int"}}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_CTRUNC", "name": "MSG_CTRUNC", "setter_type": null, "type": "builtins.int"}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "setter_type": null, "type": "builtins.int"}}, "MSG_DONTWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_DONTWAIT", "name": "MSG_DONTWAIT", "setter_type": null, "type": "builtins.int"}}, "MSG_EOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_EOR", "name": "MSG_EOR", "setter_type": null, "type": "builtins.int"}}, "MSG_ERRQUEUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_ERRQUEUE", "name": "MSG_ERRQUEUE", "setter_type": null, "type": "builtins.int"}}, "MSG_FASTOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_FASTOPEN", "name": "MSG_FASTOPEN", "setter_type": null, "type": "builtins.int"}}, "MSG_MORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_MORE", "name": "MSG_MORE", "setter_type": null, "type": "builtins.int"}}, "MSG_NOSIGNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_NOSIGNAL", "name": "MSG_NOSIGNAL", "setter_type": null, "type": "builtins.int"}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_OOB", "name": "MSG_OOB", "setter_type": null, "type": "builtins.int"}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_PEEK", "name": "MSG_PEEK", "setter_type": null, "type": "builtins.int"}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_TRUNC", "name": "MSG_TRUNC", "setter_type": null, "type": "builtins.int"}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.MSG_WAITALL", "name": "MSG_WAITALL", "setter_type": null, "type": "builtins.int"}}, "NETLINK_CRYPTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_CRYPTO", "name": "NETLINK_CRYPTO", "setter_type": null, "type": "builtins.int"}}, "NETLINK_DNRTMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_DNRTMSG", "name": "NETLINK_DNRTMSG", "setter_type": null, "type": "builtins.int"}}, "NETLINK_FIREWALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_FIREWALL", "name": "NETLINK_FIREWALL", "setter_type": null, "type": "builtins.int"}}, "NETLINK_IP6_FW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_IP6_FW", "name": "NETLINK_IP6_FW", "setter_type": null, "type": "builtins.int"}}, "NETLINK_NFLOG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_NFLOG", "name": "NETLINK_NFLOG", "setter_type": null, "type": "builtins.int"}}, "NETLINK_ROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_ROUTE", "name": "NETLINK_ROUTE", "setter_type": null, "type": "builtins.int"}}, "NETLINK_USERSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_USERSOCK", "name": "NETLINK_USERSOCK", "setter_type": null, "type": "builtins.int"}}, "NETLINK_XFRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NETLINK_XFRM", "name": "NETLINK_XFRM", "setter_type": null, "type": "builtins.int"}}, "NI_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_DGRAM", "name": "NI_DGRAM", "setter_type": null, "type": "builtins.int"}}, "NI_MAXHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_MAXHOST", "name": "NI_MAXHOST", "setter_type": null, "type": "builtins.int"}}, "NI_MAXSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_MAXSERV", "name": "NI_MAXSERV", "setter_type": null, "type": "builtins.int"}}, "NI_NAMEREQD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_NAMEREQD", "name": "NI_NAMEREQD", "setter_type": null, "type": "builtins.int"}}, "NI_NOFQDN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_NOFQDN", "name": "NI_NOFQDN", "setter_type": null, "type": "builtins.int"}}, "NI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_NUMERICHOST", "name": "NI_NUMERICHOST", "setter_type": null, "type": "builtins.int"}}, "NI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.NI_NUMERICSERV", "name": "NI_NUMERICSERV", "setter_type": null, "type": "builtins.int"}}, "PACKET_BROADCAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_BROADCAST", "name": "PACKET_BROADCAST", "setter_type": null, "type": "builtins.int"}}, "PACKET_FASTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_FASTROUTE", "name": "PACKET_FASTROUTE", "setter_type": null, "type": "builtins.int"}}, "PACKET_HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_HOST", "name": "PACKET_HOST", "setter_type": null, "type": "builtins.int"}}, "PACKET_LOOPBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_LOOPBACK", "name": "PACKET_LOOPBACK", "setter_type": null, "type": "builtins.int"}}, "PACKET_MULTICAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_MULTICAST", "name": "PACKET_MULTICAST", "setter_type": null, "type": "builtins.int"}}, "PACKET_OTHERHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_OTHERHOST", "name": "PACKET_OTHERHOST", "setter_type": null, "type": "builtins.int"}}, "PACKET_OUTGOING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PACKET_OUTGOING", "name": "PACKET_OUTGOING", "setter_type": null, "type": "builtins.int"}}, "PF_CAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PF_CAN", "name": "PF_CAN", "setter_type": null, "type": "builtins.int"}}, "PF_PACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PF_PACKET", "name": "PF_PACKET", "setter_type": null, "type": "builtins.int"}}, "PF_RDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.PF_RDS", "name": "PF_RDS", "setter_type": null, "type": "builtins.int"}}, "RDS_CANCEL_SENT_TO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CANCEL_SENT_TO", "name": "RDS_CANCEL_SENT_TO", "setter_type": null, "type": "builtins.int"}}, "RDS_CMSG_RDMA_ARGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CMSG_RDMA_ARGS", "name": "RDS_CMSG_RDMA_ARGS", "setter_type": null, "type": "builtins.int"}}, "RDS_CMSG_RDMA_DEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CMSG_RDMA_DEST", "name": "RDS_CMSG_RDMA_DEST", "setter_type": null, "type": "builtins.int"}}, "RDS_CMSG_RDMA_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CMSG_RDMA_MAP", "name": "RDS_CMSG_RDMA_MAP", "setter_type": null, "type": "builtins.int"}}, "RDS_CMSG_RDMA_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CMSG_RDMA_STATUS", "name": "RDS_CMSG_RDMA_STATUS", "setter_type": null, "type": "builtins.int"}}, "RDS_CONG_MONITOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_CONG_MONITOR", "name": "RDS_CONG_MONITOR", "setter_type": null, "type": "builtins.int"}}, "RDS_FREE_MR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_FREE_MR", "name": "RDS_FREE_MR", "setter_type": null, "type": "builtins.int"}}, "RDS_GET_MR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_GET_MR", "name": "RDS_GET_MR", "setter_type": null, "type": "builtins.int"}}, "RDS_GET_MR_FOR_DEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_GET_MR_FOR_DEST", "name": "RDS_GET_MR_FOR_DEST", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_DONTWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_DONTWAIT", "name": "RDS_RDMA_DONTWAIT", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_FENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_FENCE", "name": "RDS_RDMA_FENCE", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_INVALIDATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_INVALIDATE", "name": "RDS_RDMA_INVALIDATE", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_NOTIFY_ME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_NOTIFY_ME", "name": "RDS_RDMA_NOTIFY_ME", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_READWRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_READWRITE", "name": "RDS_RDMA_READWRITE", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_SILENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_SILENT", "name": "RDS_RDMA_SILENT", "setter_type": null, "type": "builtins.int"}}, "RDS_RDMA_USE_ONCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RDMA_USE_ONCE", "name": "RDS_RDMA_USE_ONCE", "setter_type": null, "type": "builtins.int"}}, "RDS_RECVERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.RDS_RECVERR", "name": "RDS_RECVERR", "setter_type": null, "type": "builtins.int"}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SCM_CREDENTIALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_CREDENTIALS", "name": "SCM_CREDENTIALS", "setter_type": null, "type": "builtins.int"}}, "SCM_J1939_DEST_ADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_J1939_DEST_ADDR", "name": "SCM_J1939_DEST_ADDR", "setter_type": null, "type": "builtins.int"}}, "SCM_J1939_DEST_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_J1939_DEST_NAME", "name": "SCM_J1939_DEST_NAME", "setter_type": null, "type": "builtins.int"}}, "SCM_J1939_ERRQUEUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_J1939_ERRQUEUE", "name": "SCM_J1939_ERRQUEUE", "setter_type": null, "type": "builtins.int"}}, "SCM_J1939_PRIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_J1939_PRIO", "name": "SCM_J1939_PRIO", "setter_type": null, "type": "builtins.int"}}, "SCM_RIGHTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SCM_RIGHTS", "name": "SCM_RIGHTS", "setter_type": null, "type": "builtins.int"}}, "SHUT_RD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SHUT_RD", "name": "SHUT_RD", "setter_type": null, "type": "builtins.int"}}, "SHUT_RDWR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SHUT_RDWR", "name": "SHUT_RDWR", "setter_type": null, "type": "builtins.int"}}, "SHUT_WR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SHUT_WR", "name": "SHUT_WR", "setter_type": null, "type": "builtins.int"}}, "SOCK_CLOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_CLOEXEC", "name": "SOCK_CLOEXEC", "setter_type": null, "type": "builtins.int"}}, "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_DGRAM", "name": "SOCK_DGRAM", "setter_type": null, "type": "builtins.int"}}, "SOCK_NONBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_NONBLOCK", "name": "SOCK_NONBLOCK", "setter_type": null, "type": "builtins.int"}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_RAW", "name": "SOCK_RAW", "setter_type": null, "type": "builtins.int"}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_RDM", "name": "SOCK_RDM", "setter_type": null, "type": "builtins.int"}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "setter_type": null, "type": "builtins.int"}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOCK_STREAM", "name": "SOCK_STREAM", "setter_type": null, "type": "builtins.int"}}, "SOL_ALG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_ALG", "name": "SOL_ALG", "setter_type": null, "type": "builtins.int"}}, "SOL_ATALK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_ATALK", "name": "SOL_ATALK", "setter_type": null, "type": "builtins.int"}}, "SOL_AX25": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_AX25", "name": "SOL_AX25", "setter_type": null, "type": "builtins.int"}}, "SOL_CAN_BASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_CAN_BASE", "name": "SOL_CAN_BASE", "setter_type": null, "type": "builtins.int"}}, "SOL_CAN_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_CAN_RAW", "name": "SOL_CAN_RAW", "setter_type": null, "type": "builtins.int"}}, "SOL_HCI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_HCI", "name": "SOL_HCI", "setter_type": null, "type": "builtins.int"}}, "SOL_IP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_IP", "name": "SOL_IP", "setter_type": null, "type": "builtins.int"}}, "SOL_IPX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_IPX", "name": "SOL_IPX", "setter_type": null, "type": "builtins.int"}}, "SOL_NETROM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_NETROM", "name": "SOL_NETROM", "setter_type": null, "type": "builtins.int"}}, "SOL_RDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_RDS", "name": "SOL_RDS", "setter_type": null, "type": "builtins.int"}}, "SOL_ROSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_ROSE", "name": "SOL_ROSE", "setter_type": null, "type": "builtins.int"}}, "SOL_SOCKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_SOCKET", "name": "SOL_SOCKET", "setter_type": null, "type": "builtins.int"}}, "SOL_TCP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_TCP", "name": "SOL_TCP", "setter_type": null, "type": "builtins.int"}}, "SOL_TIPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_TIPC", "name": "SOL_TIPC", "setter_type": null, "type": "builtins.int"}}, "SOL_UDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOL_UDP", "name": "SOL_UDP", "setter_type": null, "type": "builtins.int"}}, "SOMAXCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SOMAXCONN", "name": "SOMAXCONN", "setter_type": null, "type": "builtins.int"}}, "SO_ACCEPTCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_ACCEPTCONN", "name": "SO_ACCEPTCONN", "setter_type": null, "type": "builtins.int"}}, "SO_BINDTODEVICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_BINDTODEVICE", "name": "SO_BINDTODEVICE", "setter_type": null, "type": "builtins.int"}}, "SO_BROADCAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_BROADCAST", "name": "SO_BROADCAST", "setter_type": null, "type": "builtins.int"}}, "SO_DEBUG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_DEBUG", "name": "SO_DEBUG", "setter_type": null, "type": "builtins.int"}}, "SO_DOMAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_DOMAIN", "name": "SO_DOMAIN", "setter_type": null, "type": "builtins.int"}}, "SO_DONTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_DONTROUTE", "name": "SO_DONTROUTE", "setter_type": null, "type": "builtins.int"}}, "SO_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_ERROR", "name": "SO_ERROR", "setter_type": null, "type": "builtins.int"}}, "SO_J1939_ERRQUEUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_J1939_ERRQUEUE", "name": "SO_J1939_ERRQUEUE", "setter_type": null, "type": "builtins.int"}}, "SO_J1939_FILTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_J1939_FILTER", "name": "SO_J1939_FILTER", "setter_type": null, "type": "builtins.int"}}, "SO_J1939_PROMISC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_J1939_PROMISC", "name": "SO_J1939_PROMISC", "setter_type": null, "type": "builtins.int"}}, "SO_J1939_SEND_PRIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_J1939_SEND_PRIO", "name": "SO_J1939_SEND_PRIO", "setter_type": null, "type": "builtins.int"}}, "SO_KEEPALIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_KEEPALIVE", "name": "SO_KEEPALIVE", "setter_type": null, "type": "builtins.int"}}, "SO_LINGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_LINGER", "name": "SO_LINGER", "setter_type": null, "type": "builtins.int"}}, "SO_MARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_MARK", "name": "SO_MARK", "setter_type": null, "type": "builtins.int"}}, "SO_OOBINLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_OOBINLINE", "name": "SO_OOBINLINE", "setter_type": null, "type": "builtins.int"}}, "SO_PASSCRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PASSCRED", "name": "SO_PASSCRED", "setter_type": null, "type": "builtins.int"}}, "SO_PASSSEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PASSSEC", "name": "SO_PASSSEC", "setter_type": null, "type": "builtins.int"}}, "SO_PEERCRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PEERCRED", "name": "SO_PEERCRED", "setter_type": null, "type": "builtins.int"}}, "SO_PEERSEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PEERSEC", "name": "SO_PEERSEC", "setter_type": null, "type": "builtins.int"}}, "SO_PRIORITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PRIORITY", "name": "SO_PRIORITY", "setter_type": null, "type": "builtins.int"}}, "SO_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_PROTOCOL", "name": "SO_PROTOCOL", "setter_type": null, "type": "builtins.int"}}, "SO_RCVBUF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_RCVBUF", "name": "SO_RCVBUF", "setter_type": null, "type": "builtins.int"}}, "SO_RCVLOWAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_RCVLOWAT", "name": "SO_RCVLOWAT", "setter_type": null, "type": "builtins.int"}}, "SO_RCVTIMEO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_RCVTIMEO", "name": "SO_RCVTIMEO", "setter_type": null, "type": "builtins.int"}}, "SO_REUSEADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_REUSEADDR", "name": "SO_REUSEADDR", "setter_type": null, "type": "builtins.int"}}, "SO_REUSEPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_REUSEPORT", "name": "SO_REUSEPORT", "setter_type": null, "type": "builtins.int"}}, "SO_SNDBUF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_SNDBUF", "name": "SO_SNDBUF", "setter_type": null, "type": "builtins.int"}}, "SO_SNDLOWAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_SNDLOWAT", "name": "SO_SNDLOWAT", "setter_type": null, "type": "builtins.int"}}, "SO_SNDTIMEO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_SNDTIMEO", "name": "SO_SNDTIMEO", "setter_type": null, "type": "builtins.int"}}, "SO_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_TYPE", "name": "SO_TYPE", "setter_type": null, "type": "builtins.int"}}, "SO_VM_SOCKETS_BUFFER_MAX_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_VM_SOCKETS_BUFFER_MAX_SIZE", "name": "SO_VM_SOCKETS_BUFFER_MAX_SIZE", "setter_type": null, "type": "builtins.int"}}, "SO_VM_SOCKETS_BUFFER_MIN_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_VM_SOCKETS_BUFFER_MIN_SIZE", "name": "SO_VM_SOCKETS_BUFFER_MIN_SIZE", "setter_type": null, "type": "builtins.int"}}, "SO_VM_SOCKETS_BUFFER_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.SO_VM_SOCKETS_BUFFER_SIZE", "name": "SO_VM_SOCKETS_BUFFER_SIZE", "setter_type": null, "type": "builtins.int"}}, "SocketType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_socket.SocketType", "line": 809, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "_socket.socket"}}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TCP_CONGESTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_CONGESTION", "name": "TCP_CONGESTION", "setter_type": null, "type": "builtins.int"}}, "TCP_CORK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_CORK", "name": "TCP_CORK", "setter_type": null, "type": "builtins.int"}}, "TCP_DEFER_ACCEPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_DEFER_ACCEPT", "name": "TCP_DEFER_ACCEPT", "setter_type": null, "type": "builtins.int"}}, "TCP_FASTOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_FASTOPEN", "name": "TCP_FASTOPEN", "setter_type": null, "type": "builtins.int"}}, "TCP_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_INFO", "name": "TCP_INFO", "setter_type": null, "type": "builtins.int"}}, "TCP_KEEPCNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_KEEPCNT", "name": "TCP_KEEPCNT", "setter_type": null, "type": "builtins.int"}}, "TCP_KEEPIDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_KEEPIDLE", "name": "TCP_KEEPIDLE", "setter_type": null, "type": "builtins.int"}}, "TCP_KEEPINTVL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_KEEPINTVL", "name": "TCP_KEEPINTVL", "setter_type": null, "type": "builtins.int"}}, "TCP_LINGER2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_LINGER2", "name": "TCP_LINGER2", "setter_type": null, "type": "builtins.int"}}, "TCP_MAXSEG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_MAXSEG", "name": "TCP_MAXSEG", "setter_type": null, "type": "builtins.int"}}, "TCP_NODELAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_NODELAY", "name": "TCP_NODELAY", "setter_type": null, "type": "builtins.int"}}, "TCP_NOTSENT_LOWAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_NOTSENT_LOWAT", "name": "TCP_NOTSENT_LOWAT", "setter_type": null, "type": "builtins.int"}}, "TCP_QUICKACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_QUICKACK", "name": "TCP_QUICKACK", "setter_type": null, "type": "builtins.int"}}, "TCP_SYNCNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_SYNCNT", "name": "TCP_SYNCNT", "setter_type": null, "type": "builtins.int"}}, "TCP_USER_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_USER_TIMEOUT", "name": "TCP_USER_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "TCP_WINDOW_CLAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TCP_WINDOW_CLAMP", "name": "TCP_WINDOW_CLAMP", "setter_type": null, "type": "builtins.int"}}, "TIPC_ADDR_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_ADDR_ID", "name": "TIPC_ADDR_ID", "setter_type": null, "type": "builtins.int"}}, "TIPC_ADDR_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_ADDR_NAME", "name": "TIPC_ADDR_NAME", "setter_type": null, "type": "builtins.int"}}, "TIPC_ADDR_NAMESEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_ADDR_NAMESEQ", "name": "TIPC_ADDR_NAMESEQ", "setter_type": null, "type": "builtins.int"}}, "TIPC_CFG_SRV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_CFG_SRV", "name": "TIPC_CFG_SRV", "setter_type": null, "type": "builtins.int"}}, "TIPC_CLUSTER_SCOPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_CLUSTER_SCOPE", "name": "TIPC_CLUSTER_SCOPE", "setter_type": null, "type": "builtins.int"}}, "TIPC_CONN_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_CONN_TIMEOUT", "name": "TIPC_CONN_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "TIPC_CRITICAL_IMPORTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_CRITICAL_IMPORTANCE", "name": "TIPC_CRITICAL_IMPORTANCE", "setter_type": null, "type": "builtins.int"}}, "TIPC_DEST_DROPPABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_DEST_DROPPABLE", "name": "TIPC_DEST_DROPPABLE", "setter_type": null, "type": "builtins.int"}}, "TIPC_HIGH_IMPORTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_HIGH_IMPORTANCE", "name": "TIPC_HIGH_IMPORTANCE", "setter_type": null, "type": "builtins.int"}}, "TIPC_IMPORTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_IMPORTANCE", "name": "TIPC_IMPORTANCE", "setter_type": null, "type": "builtins.int"}}, "TIPC_LOW_IMPORTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_LOW_IMPORTANCE", "name": "TIPC_LOW_IMPORTANCE", "setter_type": null, "type": "builtins.int"}}, "TIPC_MEDIUM_IMPORTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_MEDIUM_IMPORTANCE", "name": "TIPC_MEDIUM_IMPORTANCE", "setter_type": null, "type": "builtins.int"}}, "TIPC_NODE_SCOPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_NODE_SCOPE", "name": "TIPC_NODE_SCOPE", "setter_type": null, "type": "builtins.int"}}, "TIPC_PUBLISHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_PUBLISHED", "name": "TIPC_PUBLISHED", "setter_type": null, "type": "builtins.int"}}, "TIPC_SRC_DROPPABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_SRC_DROPPABLE", "name": "TIPC_SRC_DROPPABLE", "setter_type": null, "type": "builtins.int"}}, "TIPC_SUBSCR_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_SUBSCR_TIMEOUT", "name": "TIPC_SUBSCR_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "TIPC_SUB_CANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_SUB_CANCEL", "name": "TIPC_SUB_CANCEL", "setter_type": null, "type": "builtins.int"}}, "TIPC_SUB_PORTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_SUB_PORTS", "name": "TIPC_SUB_PORTS", "setter_type": null, "type": "builtins.int"}}, "TIPC_SUB_SERVICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_SUB_SERVICE", "name": "TIPC_SUB_SERVICE", "setter_type": null, "type": "builtins.int"}}, "TIPC_TOP_SRV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_TOP_SRV", "name": "TIPC_TOP_SRV", "setter_type": null, "type": "builtins.int"}}, "TIPC_WAIT_FOREVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_WAIT_FOREVER", "name": "TIPC_WAIT_FOREVER", "setter_type": null, "type": "builtins.int"}}, "TIPC_WITHDRAWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_WITHDRAWN", "name": "TIPC_WITHDRAWN", "setter_type": null, "type": "builtins.int"}}, "TIPC_ZONE_SCOPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.TIPC_ZONE_SCOPE", "name": "TIPC_ZONE_SCOPE", "setter_type": null, "type": "builtins.int"}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UDPLITE_RECV_CSCOV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.UDPLITE_RECV_CSCOV", "name": "UDPLITE_RECV_CSCOV", "setter_type": null, "type": "builtins.int"}}, "UDPLITE_SEND_CSCOV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.UDPLITE_SEND_CSCOV", "name": "UDPLITE_SEND_CSCOV", "setter_type": null, "type": "builtins.int"}}, "VMADDR_CID_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.VMADDR_CID_ANY", "name": "VMADDR_CID_ANY", "setter_type": null, "type": "builtins.int"}}, "VMADDR_CID_HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.VMADDR_CID_HOST", "name": "VMADDR_CID_HOST", "setter_type": null, "type": "builtins.int"}}, "VMADDR_PORT_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.VMADDR_PORT_ANY", "name": "VMADDR_PORT_ANY", "setter_type": null, "type": "builtins.int"}}, "VM_SOCKETS_INVALID_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.VM_SOCKETS_INVALID_VERSION", "name": "VM_SOCKETS_INVALID_VERSION", "setter_type": null, "type": "builtins.int"}}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_socket._Address", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}}}, "_CMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_socket._CMSG", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_CMSGArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_socket._CMSGArg", "line": 9, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "typing_extensions.Buffer"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_RetAddress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_socket._RetAddress", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "close": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing.SupportsIndex"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.dup", "name": "dup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing.SupportsIndex"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dup", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error": {".class": "SymbolTableNode", "cross_ref": "socket.error", "kind": "Gdef"}, "gaierror": {".class": "SymbolTableNode", "cross_ref": "socket.gaierror", "kind": "Gdef"}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getaddrinfo", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdefaulttimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getdefaulttimeout", "name": "getdefaulttimeout", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getdefaulttimeout", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.gethostbyaddr", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.gethostbyname", "name": "gethostbyname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gethostbyname", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyname_ex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.gethostbyname_ex", "name": "gethostbyname_ex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gethostbyname_ex", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.gethostname", "name": "gethostname", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gethostname", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getnameinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getnameinfo", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getprotobyname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getprotobyname", "name": "getprotobyname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getprotobyname", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getservbyname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getservbyname", "name": "getservbyname", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getservbyname", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getservbyport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.getservbyport", "name": "getservbyport", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getservbyport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_ipv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_socket.has_ipv6", "name": "has_ipv6", "setter_type": null, "type": "builtins.bool"}}, "herror": {".class": "SymbolTableNode", "cross_ref": "socket.herror", "kind": "Gdef"}, "htonl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.htonl", "name": "htonl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "htonl", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "htons": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.htons", "name": "htons", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "htons", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "if_indextoname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.if_indextoname", "name": "if_indextoname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "if_indextoname", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "if_nameindex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.if_nameindex", "name": "if_nameindex", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "if_nameindex", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "if_nametoindex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.if_nametoindex", "name": "if_nametoindex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "if_nametoindex", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inet_aton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.inet_aton", "name": "inet_aton", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inet_aton", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inet_ntoa": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.inet_ntoa", "name": "inet_ntoa", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.Buffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inet_ntoa", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inet_ntop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.inet_ntop", "name": "inet_ntop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "typing_extensions.Buffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inet_ntop", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inet_pton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.inet_pton", "name": "inet_pton", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inet_pton", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ntohl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.ntohl", "name": "ntohl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ntohl", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ntohs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.ntohs", "name": "ntohs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ntohs", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setdefaulttimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.setdefaulttimeout", "name": "setdefaulttimeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setdefaulttimeout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sethostname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.sethostname", "name": "sethostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sethostname", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_socket.socket", "name": "socket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_socket.socket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_socket", "mro": ["_socket.socket", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bind of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_ex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.connect_ex", "name": "connect_ex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect_ex of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "detach of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "family": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.family", "name": "family", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_socket.socket.family", "name": "family", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fileno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.fileno", "name": "fileno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fileno of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getblocking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.getblocking", "name": "getblocking", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getblocking of socket", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpeername": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.getpeername", "name": "getpeername", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getpeername of socket", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getsockname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.getsockname", "name": "getsockname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockname of socket", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getsockopt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_socket.socket.getsockopt", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.getsockopt", "name": "getsockopt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.getsockopt", "name": "getsockopt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.getsockopt", "name": "getsockopt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.getsockopt", "name": "getsockopt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getsockopt of socket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "gettimeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.gettimeout", "name": "gettimeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gettimeout of socket", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.listen", "name": "listen", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["_socket.socket", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "listen of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.proto", "name": "proto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "proto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_socket.socket.proto", "name": "proto", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "proto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recv", "name": "recv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv of socket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recv_into", "name": "recv_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv_into of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recvfrom", "name": "recvfrom", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recvfrom of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvfrom_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recvfrom_into", "name": "recvfrom_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recvfrom_into of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recvmsg", "name": "recvmsg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recvmsg of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._CMSG"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvmsg_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.recvmsg_into", "name": "recvmsg_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recvmsg_into of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._CMSG"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.sendall", "name": "sendall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendall of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.sendmsg", "name": "sendmsg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null], "arg_types": ["_socket.socket", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._CMSGArg"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendmsg of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendmsg_afalg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 5, 5, 5], "arg_names": ["self", "msg", "op", "iv", "assoclen", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.sendmsg_afalg", "name": "sendmsg_afalg", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5, 5, 5], "arg_names": ["self", "msg", "op", "iv", "assoclen", "flags"], "arg_types": ["_socket.socket", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendmsg_afalg of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_socket.socket.sendto", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.sendto", "name": "sendto", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.sendto", "name": "sendto", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "typing_extensions.Buffer", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setblocking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.setblocking", "name": "setblocking", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setblocking of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setsockopt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_socket.socket.setsockopt", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.setsockopt", "name": "setsockopt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "typing_extensions.Buffer"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.setsockopt", "name": "setsockopt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "typing_extensions.Buffer"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.setsockopt", "name": "setsockopt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "NoneType"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_socket.socket.setsockopt", "name": "setsockopt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "NoneType"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "typing_extensions.Buffer"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["_socket.socket", "builtins.int", "builtins.int", {".class": "NoneType"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setsockopt of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "settimeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.settimeout", "name": "settimeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "settimeout of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "_socket.socket.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_socket.socket", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shutdown of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "timeout of socket", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_socket.socket.timeout", "name": "timeout", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "timeout of socket", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "_socket.socket.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_socket.socket.type", "name": "type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_socket.socket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "socketpair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_socket.socketpair", "name": "socketpair", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "socketpair", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_socket.socket", "_socket.socket"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timeout": {".class": "SymbolTableNode", "cross_ref": "socket.timeout", "kind": "Gdef"}}, "path": "/home/<USER>/miniconda3/envs/exp/lib/python3.9/site-packages/mypy/typeshed/stdlib/_socket.pyi"}