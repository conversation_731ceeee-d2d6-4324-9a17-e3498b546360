{"data_mtime": 1754115486, "dep_lines": [1, 2, 3, 4, 5, 6, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "email._policybase", "email.contentmanager", "email.message", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "3d8a76ddc4e76139fdc5606031768a0eab26f1ea", "id": "email.policy", "ignore_all": true, "interface_hash": "d3e25fd0a51fa0e8a05a956be61b9b62f1ea0f4b", "mtime": 1754115453, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniconda3/envs/exp/lib/python3.9/site-packages/mypy/typeshed/stdlib/email/policy.pyi", "plugin_data": null, "size": 2813, "suppressed": [], "version_id": "1.17.1"}