{".class": "MypyFile", "_fullname": "typing_extensions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "Annotated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Annotated", "name": "Annotated", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "AnnotationForm": {".class": "SymbolTableNode", "cross_ref": "_typeshed.AnnotationForm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef"}, "AsyncContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractAsyncContextManager", "kind": "Gdef"}, "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Buffer", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol", "runtime_protocol"], "fullname": "typing_extensions.Buffer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Buffer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__buffer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Buffer.__buffer__", "name": "__buffer__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__buffer__ of <PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CapsuleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.CapsuleType", "name": "CapsuleType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.CapsuleType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.CapsuleType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChainMap": {".class": "SymbolTableNode", "cross_ref": "typing.ChainMap", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Concatenate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Concatenate", "name": "Concatenate", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Container": {".class": "SymbolTableNode", "cross_ref": "typing.Container", "kind": "Gdef"}, "ContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef"}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "typing.Counter", "kind": "Gdef"}, "DefaultDict": {".class": "SymbolTableNode", "cross_ref": "typing.DefaultDict", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Doc", "name": "Doc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions.Doc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Doc", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Doc.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.Doc", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of Doc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Doc.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.Doc"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__hash__ of Doc", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Doc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.Doc", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Doc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "documentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "typing_extensions.Doc.documentation", "name": "documentation", "setter_type": null, "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Final", "name": "Final", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Format", "name": "Format", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "typing_extensions.Format", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Format", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FORWARDREF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "typing_extensions.Format.FORWARDREF", "name": "FORWARDREF", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "STRING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "typing_extensions.Format.STRING", "name": "STRING", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "typing_extensions.Format.VALUE", "name": "VALUE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "VALUE_WITH_FAKE_GLOBALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "typing_extensions.Format.VALUE_WITH_FAKE_GLOBALS", "name": "VALUE_WITH_FAKE_GLOBALS", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "IdentityFunction": {".class": "SymbolTableNode", "cross_ref": "_typeshed.IdentityFunction", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntVar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.IntVar", "name": "IntVar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "IntVar", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ItemsView": {".class": "SymbolTableNode", "cross_ref": "typing.ItemsView", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KeysView": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Literal", "name": "Literal", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "LiteralString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": -1, "fullname": "typing_extensions.LiteralString", "line": -1, "no_args": true, "normalized": true, "python_3_12_type_alias": false, "target": "builtins.str"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MappingView": {".class": "SymbolTableNode", "cross_ref": "typing.MappingView", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef"}, "MutableSet": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSet", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.NamedTuple", "name": "NamedTuple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions.NamedTuple", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.NamedTuple", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions.NamedTuple.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "typename", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.NamedTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "typename", "fields"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.NamedTuple.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "typename", "fields"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "typename", "fields", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.NamedTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "typename", "fields", "kwargs"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.NamedTuple.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "typename", "fields", "kwargs"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "typename", "fields"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "typename", "fields", "kwargs"], "arg_types": ["typing_extensions.NamedTuple", "builtins.str", {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NamedTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__orig_bases__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions.NamedTuple.__orig_bases__", "name": "__orig_bases__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.NamedTuple._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.NamedTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_asdict of NamedTuple", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions.NamedTuple._field_defaults", "name": "_field_defaults", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions.NamedTuple._fields", "name": "_fields", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "typing_extensions.NamedTuple._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of NamedTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "typing_extensions.NamedTuple._make", "name": "_make", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of NamedTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}]}}}}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.NamedTuple._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace of NamedTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions.NamedTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions.NamedTuple", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Never": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Never", "name": "Never", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "NewType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.NewType", "name": "NewType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions.NewType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.NewType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.NewType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.NewType", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.NewType.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of NewType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.NewType.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.NewType.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.NewType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "tp"], "arg_types": ["typing_extensions.NewType", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NewType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__supertype__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "typing_extensions.NewType.__supertype__", "name": "__supertype__", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.type", "typing_extensions.NewType"], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.NoDefault", "name": "No<PERSON><PERSON><PERSON>", "setter_type": null, "type": "typing_extensions._NoDefaultType"}}, "NoExtraItems": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.NoExtraItems", "name": "NoExtraItems", "setter_type": null, "type": "typing_extensions._NoExtraItemsType"}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NotRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.NotRequired", "name": "NotRequired", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": -1, "fullname": "typing_extensions.OrderedDict", "line": -1, "no_args": true, "normalized": true, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.OrderedDict"}}}, "ParamSpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.ParamSpec", "name": "ParamSpec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.ParamSpec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.ParamSpec", "builtins.object"], "names": {".class": "SymbolTable", "__bound__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__bound__", "name": "__bound__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bound__ of ParamSpec", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__bound__", "name": "__bound__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bound__ of ParamSpec", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__contravariant__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__contravariant__", "name": "__contravariant__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__contravariant__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__contravariant__", "name": "__contravariant__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__contravariant__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__covariant__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__covariant__", "name": "__covariant__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__covariant__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__covariant__", "name": "__covariant__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__covariant__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__default__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__default__", "name": "__default__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of ParamSpec", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__default__", "name": "__default__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of ParamSpec", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__infer_variance__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__infer_variance__", "name": "__infer_variance__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__infer_variance__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__infer_variance__", "name": "__infer_variance__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__infer_variance__ of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "name", "bound", "contravariant", "covariant", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "name", "bound", "contravariant", "covariant", "default"], "arg_types": ["typing_extensions.ParamSpec", "builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "builtins.str"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParamSpec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of ParamSpec", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.__name__", "name": "__name__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of ParamSpec", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__typing_prepare_subst__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.ParamSpec.__typing_prepare_subst__", "name": "__typing_prepare_subst__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "arg_types": ["typing_extensions.ParamSpec", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__typing_prepare_subst__ of ParamSpec", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.args", "name": "args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "args of ParamSpec", "ret_type": "typing_extensions.ParamSpecArgs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.args", "name": "args", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "args of ParamSpec", "ret_type": "typing_extensions.ParamSpecArgs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.ParamSpec.has_default", "name": "has_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_default of ParamSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpec.kwargs", "name": "kwargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kwargs of ParamSpec", "ret_type": "typing_extensions.ParamSpecKwargs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpec.kwargs", "name": "kwargs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kwargs of ParamSpec", "ret_type": "typing_extensions.ParamSpecKwargs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamSpecArgs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.ParamSpecArgs", "name": "ParamSpecArgs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.ParamSpecArgs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.ParamSpecArgs", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.ParamSpecArgs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["typing_extensions.ParamSpecArgs", "typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParamSpecArgs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__origin__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpecArgs.__origin__", "name": "__origin__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpecArgs"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__origin__ of ParamSpecArgs", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpecArgs.__origin__", "name": "__origin__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpecArgs"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__origin__ of ParamSpecArgs", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamSpecKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.ParamSpecKwargs", "name": "ParamSpecKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.ParamSpecKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.ParamSpecKwargs", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.ParamSpecKwargs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["typing_extensions.ParamSpecKwargs", "typing_extensions.ParamSpec"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParamSpecKwargs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__origin__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.ParamSpecKwargs.__origin__", "name": "__origin__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpecKwargs"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__origin__ of ParamSpecKwargs", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.ParamSpecKwargs.__origin__", "name": "__origin__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.ParamSpecKwargs"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__origin__ of ParamSpecKwargs", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Protocol", "name": "Protocol", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "ReadOnly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.ReadOnly", "name": "Read<PERSON>nly", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Reader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["read", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Reader", "name": "Reader", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.Reader", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.Reader", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Reader", "builtins.object"], "names": {".class": "SymbolTable", "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.Reader.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.Reader", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.Reader"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of Reader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.Reader", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.Reader.read", "name": "read", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.Reader", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.Reader"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of Reader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.Reader", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "Required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Required", "name": "Required", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Reversible": {".class": "SymbolTableNode", "cross_ref": "typing.Reversible", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Self", "name": "Self", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Sentinel", "name": "Sentinel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions.Sentinel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Sentinel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "repr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Sentinel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "repr"], "arg_types": ["typing_extensions.Sentinel", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Sentinel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Sentinel.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.Sentinel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of Sentinel", "ret_type": "typing_extensions._SpecialForm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.Sentinel.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.Sentinel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of Sentinel", "ret_type": "typing_extensions._SpecialForm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Sized": {".class": "SymbolTableNode", "cross_ref": "typing.Sized", "kind": "Gdef"}, "SupportsAbs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__abs__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsAbs", "name": "SupportsAbs", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsAbs", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsAbs", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsAbs", "builtins.object"], "names": {".class": "SymbolTable", "__abs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsAbs.__abs__", "name": "__abs__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsAbs", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsAbs"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__abs__ of SupportsAbs", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsAbs", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsAbs.__abs__", "name": "__abs__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsAbs", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsAbs"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__abs__ of SupportsAbs", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsAbs", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "SupportsBytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__bytes__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsBytes", "name": "SupportsBytes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsBytes", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsBytes", "builtins.object"], "names": {".class": "SymbolTable", "__bytes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsBytes.__bytes__", "name": "__bytes__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.SupportsBytes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bytes__ of SupportsBytes", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsBytes.__bytes__", "name": "__bytes__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.SupportsBytes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bytes__ of SupportsBytes", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsComplex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__complex__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsComplex", "name": "SupportsComplex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsComplex", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsComplex", "builtins.object"], "names": {".class": "SymbolTable", "__complex__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsComplex.__complex__", "name": "__complex__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsComplex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__complex__ of SupportsComplex", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsComplex.__complex__", "name": "__complex__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsComplex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__complex__ of SupportsComplex", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__float__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsFloat", "name": "SupportsFloat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsFloat", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsFloat", "builtins.object"], "names": {".class": "SymbolTable", "__float__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsFloat.__float__", "name": "__float__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsFloat"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__float__ of SupportsFloat", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsFloat.__float__", "name": "__float__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsFloat"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__float__ of SupportsFloat", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsIndex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__index__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsIndex", "name": "SupportsIndex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsIndex", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsIndex", "builtins.object"], "names": {".class": "SymbolTable", "__index__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsIndex.__index__", "name": "__index__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.SupportsIndex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__index__ of SupportsIndex", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsIndex.__index__", "name": "__index__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.SupportsIndex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__index__ of SupportsIndex", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__int__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsInt", "name": "SupportsInt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsInt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsInt", "builtins.object"], "names": {".class": "SymbolTable", "__int__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsInt.__int__", "name": "__int__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsInt"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__int__ of SupportsInt", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsInt.__int__", "name": "__int__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.SupportsInt"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__int__ of SupportsInt", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsRound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__round__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.SupportsRound", "name": "SupportsRound", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.SupportsRound", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.SupportsRound", "builtins.object"], "names": {".class": "SymbolTable", "__round__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions.SupportsRound.__round__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsRound.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsRound.__round__", "name": "__round__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.SupportsRound.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.SupportsRound.__round__", "name": "__round__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing_extensions.SupportsRound"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__round__ of SupportsRound", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "id": 1, "name": "_T_co", "namespace": "typing_extensions.SupportsRound", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "typing.Text", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.TypeAlias", "name": "TypeAlias", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "TypeAliasType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.TypeAliasType", "name": "TypeAliasType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.TypeAliasType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.TypeAliasType", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.TypeAliasType", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of TypeAliasType", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "name", "value", "type_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "name", "value", "type_params"], "arg_types": ["typing_extensions.TypeAliasType", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TypeAliasType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "typing_extensions.TypeAliasType"}, "builtins.object", "builtins.object"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init_subclass__ of TypeAliasType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__module__", "name": "__module__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__module__ of TypeAliasType", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeAliasType.__module__", "name": "__module__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__module__ of TypeAliasType", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeAliasType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeAliasType.__name__", "name": "__name__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeAliasType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__parameters__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__parameters__", "name": "__parameters__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parameters__ of TypeAliasType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeAliasType.__parameters__", "name": "__parameters__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parameters__ of TypeAliasType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__type_params__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__type_params__", "name": "__type_params__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__type_params__ of TypeAliasType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeAliasType.__type_params__", "name": "__type_params__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__type_params__ of TypeAliasType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__value__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeAliasType.__value__", "name": "__value__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__value__ of TypeAliasType", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeAliasType.__value__", "name": "__value__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeAliasType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__value__ of TypeAliasType", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.TypeForm", "name": "TypeForm", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "TypeGuard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.TypeGuard", "name": "TypeGuard", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "TypeIs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.TypeIs", "name": "TypeIs", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "TypeVar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.TypeVar", "name": "TypeVar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.TypeVar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.TypeVar", "builtins.object"], "names": {".class": "SymbolTable", "__bound__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__bound__", "name": "__bound__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bound__ of TypeVar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__bound__", "name": "__bound__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bound__ of TypeVar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__constraints__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__constraints__", "name": "__constraints__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__constraints__ of TypeVar", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__constraints__", "name": "__constraints__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__constraints__ of TypeVar", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__contravariant__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__contravariant__", "name": "__contravariant__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__contravariant__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__contravariant__", "name": "__contravariant__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__contravariant__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__covariant__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__covariant__", "name": "__covariant__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__covariant__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__covariant__", "name": "__covariant__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__covariant__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__default__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__default__", "name": "__default__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of TypeVar", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__default__", "name": "__default__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of TypeVar", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__infer_variance__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__infer_variance__", "name": "__infer_variance__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__infer_variance__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__infer_variance__", "name": "__infer_variance__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__infer_variance__ of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "constraints", "bound", "covariant", "contravariant", "default", "infer_variance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "constraints", "bound", "covariant", "contravariant", "default", "infer_variance"], "arg_types": ["typing_extensions.TypeVar", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TypeVar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVar.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeVar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVar.__name__", "name": "__name__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeVar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__typing_prepare_subst__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVar.__typing_prepare_subst__", "name": "__typing_prepare_subst__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "arg_types": ["typing_extensions.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__typing_prepare_subst__ of TypeVar", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVar.has_default", "name": "has_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVar"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_default of TypeVar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVarTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.TypeVarTuple", "name": "TypeVarTuple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions.TypeVarTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.TypeVarTuple", "builtins.object"], "names": {".class": "SymbolTable", "__default__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.__default__", "name": "__default__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of TypeVarTuple", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVarTuple.__default__", "name": "__default__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__default__ of TypeVarTuple", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "name", "default"], "arg_types": ["typing_extensions.TypeVarTuple", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TypeVarTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of TypeVarTuple", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeVarTuple", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "typing_extensions.TypeVarTuple.__name__", "name": "__name__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__name__ of TypeVarTuple", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__typing_prepare_subst__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.__typing_prepare_subst__", "name": "__typing_prepare_subst__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "alias", "args"], "arg_types": ["typing_extensions.TypeVarTuple", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__typing_prepare_subst__ of TypeVarTuple", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.TypeVarTuple.has_default", "name": "has_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions.TypeVarTuple"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_default of TypeVarTuple", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.Unpack", "name": "Unpack", "setter_type": null, "type": "typing_extensions._SpecialForm"}}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValuesView": {".class": "SymbolTableNode", "cross_ref": "typing.ValuesView", "kind": "Gdef"}, "Writer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["write", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.Writer", "name": "Writer", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "id": 1, "name": "_T_contra", "namespace": "typing_extensions.Writer", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "typing_extensions.Writer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.Writer", "builtins.object"], "names": {".class": "SymbolTable", "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "typing_extensions.Writer.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "id": 1, "name": "_T_contra", "namespace": "typing_extensions.Writer", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "typing_extensions.Writer"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "id": 1, "name": "_T_contra", "namespace": "typing_extensions.Writer", "upper_bound": "builtins.object", "values": [], "variance": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of Writer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "typing_extensions.Writer.write", "name": "write", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "id": 1, "name": "_T_contra", "namespace": "typing_extensions.Writer", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "typing_extensions.Writer"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "id": 1, "name": "_T_contra", "namespace": "typing_extensions.Writer", "upper_bound": "builtins.object", "values": [], "variance": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of Writer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_T_contra"], "typeddict_type": null}}, "_Alias": {".class": "SymbolTableNode", "cross_ref": "typing._<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnnotatedAlias": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions._AnnotatedAlias", "name": "_Annotated<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "name": "_F", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_Final": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions._Final", "name": "_Final", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions._Final", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions._Final", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NoDefaultType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions._NoDefaultType", "name": "_NoDefaultType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "typing_extensions._NoDefaultType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions._NoDefaultType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NoExtraItemsType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions._NoExtraItemsType", "name": "_NoExtraItemsType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions._NoExtraItemsType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions._NoExtraItemsType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SpecialForm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing_extensions._Final"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions._SpecialForm", "name": "_SpecialForm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions._SpecialForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions._SpecialForm", "typing_extensions._Final", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._SpecialForm.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._SpecialForm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of _SpecialForm", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_TC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "name": "_TC", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_T_contra": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T_contra", "name": "_T_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "_TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TypedDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__getitem__", 1], ["__iter__", 1], ["__len__", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "typing_extensions._TypedDict", "name": "_TypedDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "typing_extensions._TypedDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__closed__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__closed__", "name": "__closed__", "setter_type": null, "type": "builtins.bool"}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "UninhabitedType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__delitem__ of TypedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__extra_items__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__extra_items__", "name": "__extra_items__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}}}, "__ior__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions._TypedDict.__ior__", "name": "__ior__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ior__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}}, "__mutable_keys__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__mutable_keys__", "name": "__mutable_keys__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__optional_keys__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__optional_keys__", "name": "__optional_keys__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions._TypedDict.__or__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions._TypedDict.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions._TypedDict.__or__", "name": "__or__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions._TypedDict.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions._TypedDict.__or__", "name": "__or__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__or__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__orig_bases__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__orig_bases__", "name": "__orig_bases__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__readonly_keys__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__readonly_keys__", "name": "__readonly_keys__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__required_keys__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__required_keys__", "name": "__required_keys__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions._TypedDict.__ror__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions._TypedDict.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions._TypedDict.__ror__", "name": "__ror__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "typing_extensions._TypedDict.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions._TypedDict.__ror__", "name": "__ror__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions._TypedDict", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ror__ of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__total__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "typing_extensions._TypedDict.__total__", "name": "__total__", "setter_type": null, "type": "builtins.bool"}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions._TypedDict.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of TypedDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions._TypedDict"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "items of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "_collections_abc.dict_items"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions._TypedDict"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keys of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "_collections_abc.dict_keys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "k", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "k", "default"], "arg_types": ["typing_extensions._TypedDict", {".class": "UninhabitedType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions._TypedDict.pop", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pop of TypedDict", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions._TypedDict.pop", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "setdefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "k", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.setdefault", "name": "set<PERSON><PERSON>ult", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "k", "default"], "arg_types": ["typing_extensions._TypedDict", {".class": "UninhabitedType"}, "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setdefault of TypedDict", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions._TypedDict.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of TypedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}]}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions._TypedDict.values", "name": "values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["typing_extensions._TypedDict"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "values of TypedDict", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "_collections_abc.dict_values"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TypedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "typing_extensions._TypedDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "typing_extensions.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "typing_extensions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "assert_never": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.assert_never", "name": "assert_never", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UninhabitedType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "assert_never", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.assert_type", "name": "assert_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.assert_type", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "assert_type", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.assert_type", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.assert_type", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "clear_overloads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.clear_overloads", "name": "clear_overloads", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear_overloads", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_transform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 4], "arg_names": ["eq_default", "order_default", "kw_only_default", "frozen_default", "field_specifiers", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.dataclass_transform", "name": "dataclass_transform", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 4], "arg_names": ["eq_default", "order_default", "kw_only_default", "frozen_default", "field_specifiers", "kwargs"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass_transform", "ret_type": "_typeshed.IdentityFunction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "typing_extensions.deprecated", "name": "deprecated", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "typing_extensions.deprecated", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "typing_extensions", "mro": ["typing_extensions.deprecated", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.deprecated.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing_extensions.deprecated", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.deprecated.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of deprecated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.deprecated.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.deprecated.__call__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "category", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "typing_extensions.deprecated.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "category", "stacklevel"], "arg_types": ["typing_extensions.deprecated", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Warning"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of deprecated", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "typing_extensions.deprecated.category", "name": "category", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Warning"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "typing_extensions.deprecated.message", "name": "message", "setter_type": null, "type": "builtins.str"}}, "stacklevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "typing_extensions.deprecated.stacklevel", "name": "stacklevel", "setter_type": null, "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "dict_items": {".class": "SymbolTableNode", "cross_ref": "_collections_abc.dict_items", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dict_keys": {".class": "SymbolTableNode", "cross_ref": "_collections_abc.dict_keys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dict_values": {".class": "SymbolTableNode", "cross_ref": "_collections_abc.dict_values", "kind": "Gdef", "module_hidden": true, "module_public": false}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "evaluate_forward_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions.evaluate_forward_ref", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Format", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.evaluate_forward_ref", "name": "evaluate_forward_ref", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Format", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 3, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["forward_ref", "owner", "globals", "locals", "type_params", "format", "_recursive_guard"], "arg_types": ["typing.ForwardRef", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "builtins.object"}, "types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["typing_extensions.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing_extensions.Format", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_forward_ref", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "final": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.final", "name": "final", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.final", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "final", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.final", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.final", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "get_annotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions.get_annotations", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "typing_extensions.Format"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_annotations", "name": "get_annotations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "typing_extensions.Format"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "STRING"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 3], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "typing_extensions.Format", "value": "FORWARDREF"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, "typing.ForwardRef"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["obj", "globals", "locals", "eval_str", "format"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "typing_extensions.Format"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.get_args", "name": "get_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_args", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_origin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "typing_extensions.get_origin", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": ["types.GenericAlias"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": ["types.GenericAlias"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "UnionType", "items": ["typing_extensions.ParamSpecArgs", "typing_extensions.ParamSpecKwargs"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "UnionType", "items": ["typing_extensions.ParamSpecArgs", "typing_extensions.ParamSpecKwargs"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "typing_extensions.get_origin", "name": "get_origin", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": ["types.GenericAlias"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "UnionType", "items": ["typing_extensions.ParamSpecArgs", "typing_extensions.ParamSpecKwargs"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": "typing_extensions.ParamSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_origin", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_original_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.get_original_bases", "name": "get_original_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_original_bases", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_overloads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.get_overloads", "name": "get_overloads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_overloads", "ret_type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_protocol_members": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.get_protocol_members", "name": "get_protocol_members", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_protocol_members", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_type_hints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "globalns", "localns", "include_extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.get_type_hints", "name": "get_type_hints", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "globalns", "localns", "include_extras"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_type_hints", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.AnnotationForm"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.is_protocol", "name": "is_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_protocol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_typeddict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.is_typeddict", "name": "is_typeddict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tp"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_typeddict", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "no_type_check_decorator": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check_decorator", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.override", "name": "override", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.override", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "override", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.override", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._F", "id": -1, "name": "_F", "namespace": "typing_extensions.override", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "reveal_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.reveal_type", "name": "reveal_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.reveal_type", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reveal_type", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.reveal_type", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._T", "id": -1, "name": "_T", "namespace": "typing_extensions.reveal_type", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "runtime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "typing_extensions.runtime", "name": "runtime", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}]}}}, "runtime_checkable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "typing_extensions.runtime_checkable", "name": "runtime_checkable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runtime_checkable", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing_extensions._TC", "id": -1, "name": "_TC", "namespace": "typing_extensions.runtime_checkable", "upper_bound": {".class": "TypeType", "item": "builtins.object"}, "values": [], "variance": 0}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/miniconda3/envs/exp/lib/python3.9/site-packages/mypy/typeshed/stdlib/typing_extensions.pyi"}