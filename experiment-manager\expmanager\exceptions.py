"""
异常定义模块
定义实验管理系统的专用异常类
"""


class ExpManagerError(Exception):
    """实验管理器基础异常类"""

    def __init__(self, message: str, error_code: str = None):
        """
        初始化异常

        Args:
            message: 错误消息
            error_code: 错误代码
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ServerNotAvailableError(ExpManagerError):
    """服务器不可用异常"""

    def __init__(self, message: str = "实验管理服务器不可用"):
        super().__init__(message, "SERVER_UNAVAILABLE")


class ExperimentNotFoundError(ExpManagerError):
    """实验未找到异常"""

    def __init__(self, experiment_id: str):
        message = f"实验未找到: {experiment_id}"
        super().__init__(message, "EXPERIMENT_NOT_FOUND")
        self.experiment_id = experiment_id


class ConfigurationError(ExpManagerError):
    """配置错误异常"""

    def __init__(self, message: str):
        super().__init__(message, "CONFIGURATION_ERROR")


class ValidationError(ExpManagerError):
    """数据验证错误异常"""

    def __init__(self, message: str, field: str = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field = field


class NetworkError(ExpManagerError):
    """网络错误异常"""

    def __init__(self, message: str):
        super().__init__(message, "NETWORK_ERROR")


class ServerStartupError(ExpManagerError):
    """服务器启动错误异常"""

    def __init__(self, message: str = "服务器启动失败"):
        super().__init__(message, "SERVER_STARTUP_ERROR")
