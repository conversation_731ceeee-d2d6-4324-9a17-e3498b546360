{".class": "MypyFile", "_fullname": "socket", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AF_ALG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ALG", "name": "AF_ALG", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_APPLETALK", "name": "AF_APPLETALK", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ASH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ASH", "name": "AF_ASH", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ATMPVC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ATMPVC", "name": "AF_ATMPVC", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ATMSVC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ATMSVC", "name": "AF_ATMSVC", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_AX25": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_AX25", "name": "AF_AX25", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_BRIDGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_BRIDGE", "name": "AF_BRIDGE", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_CAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_CAN", "name": "AF_CAN", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_DECnet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.AF_DECnet", "name": "AF_DECnet", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}}}, "AF_ECONET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ECONET", "name": "AF_ECONET", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_INET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET", "name": "AF_INET", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET6", "name": "AF_INET6", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_IPX", "name": "AF_IPX", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_IRDA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_IRDA", "name": "AF_IRDA", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_KEY", "name": "AF_KEY", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_LLC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_LLC", "name": "AF_LLC", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_NETBEUI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_NETBEUI", "name": "AF_NETBEUI", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_NETLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_NETLINK", "name": "AF_NETLINK", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_NETROM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_NETROM", "name": "AF_NETROM", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_PACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_PACKET", "name": "AF_PACKET", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_PPPOX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_PPPOX", "name": "AF_PPPOX", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_QIPCRTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_QIPCRTR", "name": "AF_QIPCRTR", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_RDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_RDS", "name": "AF_RDS", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ROSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ROSE", "name": "AF_ROSE", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ROUTE", "name": "AF_ROUTE", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_SECURITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_SECURITY", "name": "AF_SECURITY", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_SNA", "name": "AF_SNA", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_TIPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_TIPC", "name": "AF_TIPC", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_UNIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_UNIX", "name": "AF_UNIX", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_UNSPEC", "name": "AF_UNSPEC", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_VSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_VSOCK", "name": "AF_VSOCK", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_WANPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_WANPIPE", "name": "AF_WANPIPE", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_X25": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_X25", "name": "AF_X25", "setter_type": null, "type": "socket.AddressFamily"}}, "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ALL", "name": "AI_ALL", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_CANONNAME", "name": "AI_CANONNAME", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_PASSIVE", "name": "AI_PASSIVE", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_V4MAPPED", "name": "AI_V4MAPPED", "setter_type": null, "type": "socket.AddressInfo"}}, "ALG_OP_DECRYPT": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_OP_DECRYPT", "kind": "Gdef"}, "ALG_OP_ENCRYPT": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_OP_ENCRYPT", "kind": "Gdef"}, "ALG_OP_SIGN": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_OP_SIGN", "kind": "Gdef"}, "ALG_OP_VERIFY": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_OP_VERIFY", "kind": "Gdef"}, "ALG_SET_AEAD_ASSOCLEN": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_AEAD_ASSOCLEN", "kind": "Gdef"}, "ALG_SET_AEAD_AUTHSIZE": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_AEAD_AUTHSIZE", "kind": "Gdef"}, "ALG_SET_IV": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_IV", "kind": "Gdef"}, "ALG_SET_KEY": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_KEY", "kind": "Gdef"}, "ALG_SET_OP": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_OP", "kind": "Gdef"}, "ALG_SET_PUBKEY": {".class": "SymbolTableNode", "cross_ref": "_socket.ALG_SET_PUBKEY", "kind": "Gdef"}, "AddressFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressFamily", "name": "AddressFamily", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressFamily", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressFamily", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AF_ALG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ALG", "name": "AF_ALG", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 38}, "type_ref": "builtins.int"}}}, "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_APPLETALK", "name": "AF_APPLETALK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "AF_ASH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ASH", "name": "AF_ASH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 18}, "type_ref": "builtins.int"}}}, "AF_ATMPVC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ATMPVC", "name": "AF_ATMPVC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "AF_ATMSVC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ATMSVC", "name": "AF_ATMSVC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, "type_ref": "builtins.int"}}}, "AF_AX25": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_AX25", "name": "AF_AX25", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "AF_BRIDGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_BRIDGE", "name": "AF_BRIDGE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "AF_CAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_CAN", "name": "AF_CAN", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 29}, "type_ref": "builtins.int"}}}, "AF_ECONET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ECONET", "name": "AF_ECONET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 19}, "type_ref": "builtins.int"}}}, "AF_INET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET", "name": "AF_INET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET6", "name": "AF_INET6", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_IPX", "name": "AF_IPX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AF_IRDA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_IRDA", "name": "AF_IRDA", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 23}, "type_ref": "builtins.int"}}}, "AF_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_KEY", "name": "AF_KEY", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 15}, "type_ref": "builtins.int"}}}, "AF_LLC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_LLC", "name": "AF_LLC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 26}, "type_ref": "builtins.int"}}}, "AF_NETBEUI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_NETBEUI", "name": "AF_NETBEUI", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 13}, "type_ref": "builtins.int"}}}, "AF_NETLINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_NETLINK", "name": "AF_NETLINK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AF_NETROM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_NETROM", "name": "AF_NETROM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "AF_PACKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_PACKET", "name": "AF_PACKET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 17}, "type_ref": "builtins.int"}}}, "AF_PPPOX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_PPPOX", "name": "AF_PPPOX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 24}, "type_ref": "builtins.int"}}}, "AF_QIPCRTR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_QIPCRTR", "name": "AF_QIPCRTR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 42}, "type_ref": "builtins.int"}}}, "AF_RDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_RDS", "name": "AF_RDS", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 21}, "type_ref": "builtins.int"}}}, "AF_ROSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ROSE", "name": "AF_ROSE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 11}, "type_ref": "builtins.int"}}}, "AF_ROUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ROUTE", "name": "AF_ROUTE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AF_SECURITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_SECURITY", "name": "AF_SECURITY", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 14}, "type_ref": "builtins.int"}}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_SNA", "name": "AF_SNA", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 22}, "type_ref": "builtins.int"}}}, "AF_TIPC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_TIPC", "name": "AF_TIPC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, "type_ref": "builtins.int"}}}, "AF_UNIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_UNIX", "name": "AF_UNIX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_UNSPEC", "name": "AF_UNSPEC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "AF_VSOCK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_VSOCK", "name": "AF_VSOCK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 40}, "type_ref": "builtins.int"}}}, "AF_WANPIPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_WANPIPE", "name": "AF_WANPIPE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 25}, "type_ref": "builtins.int"}}}, "AF_X25": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_X25", "name": "AF_X25", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressFamily.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressFamily", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddressInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressInfo", "name": "AddressInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressInfo", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressInfo", "enum.IntFlag", "builtins.int", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ALL", "name": "AI_ALL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_CANONNAME", "name": "AI_CANONNAME", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_PASSIVE", "name": "AI_PASSIVE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_V4MAPPED", "name": "AI_V4MAPPED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedRWPair": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedRWPair", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedWriter": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedWriter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CAN_BCM": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM", "kind": "Gdef"}, "CAN_BCM_CAN_FD_FRAME": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_CAN_FD_FRAME", "kind": "Gdef"}, "CAN_BCM_RX_ANNOUNCE_RESUME": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_ANNOUNCE_RESUME", "kind": "Gdef"}, "CAN_BCM_RX_CHANGED": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_CHANGED", "kind": "Gdef"}, "CAN_BCM_RX_CHECK_DLC": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_CHECK_DLC", "kind": "Gdef"}, "CAN_BCM_RX_DELETE": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_DELETE", "kind": "Gdef"}, "CAN_BCM_RX_FILTER_ID": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_FILTER_ID", "kind": "Gdef"}, "CAN_BCM_RX_NO_AUTOTIMER": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_NO_AUTOTIMER", "kind": "Gdef"}, "CAN_BCM_RX_READ": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_READ", "kind": "Gdef"}, "CAN_BCM_RX_RTR_FRAME": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_RTR_FRAME", "kind": "Gdef"}, "CAN_BCM_RX_SETUP": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_SETUP", "kind": "Gdef"}, "CAN_BCM_RX_STATUS": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_STATUS", "kind": "Gdef"}, "CAN_BCM_RX_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_RX_TIMEOUT", "kind": "Gdef"}, "CAN_BCM_SETTIMER": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_SETTIMER", "kind": "Gdef"}, "CAN_BCM_STARTTIMER": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_STARTTIMER", "kind": "Gdef"}, "CAN_BCM_TX_ANNOUNCE": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_ANNOUNCE", "kind": "Gdef"}, "CAN_BCM_TX_COUNTEVT": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_COUNTEVT", "kind": "Gdef"}, "CAN_BCM_TX_CP_CAN_ID": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_CP_CAN_ID", "kind": "Gdef"}, "CAN_BCM_TX_DELETE": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_DELETE", "kind": "Gdef"}, "CAN_BCM_TX_EXPIRED": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_EXPIRED", "kind": "Gdef"}, "CAN_BCM_TX_READ": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_READ", "kind": "Gdef"}, "CAN_BCM_TX_RESET_MULTI_IDX": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_RESET_MULTI_IDX", "kind": "Gdef"}, "CAN_BCM_TX_SEND": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_SEND", "kind": "Gdef"}, "CAN_BCM_TX_SETUP": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_SETUP", "kind": "Gdef"}, "CAN_BCM_TX_STATUS": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_BCM_TX_STATUS", "kind": "Gdef"}, "CAN_EFF_FLAG": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_EFF_FLAG", "kind": "Gdef"}, "CAN_EFF_MASK": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_EFF_MASK", "kind": "Gdef"}, "CAN_ERR_FLAG": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_ERR_FLAG", "kind": "Gdef"}, "CAN_ERR_MASK": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_ERR_MASK", "kind": "Gdef"}, "CAN_ISOTP": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_ISOTP", "kind": "Gdef"}, "CAN_J1939": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_J1939", "kind": "Gdef"}, "CAN_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW", "kind": "Gdef"}, "CAN_RAW_ERR_FILTER": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_ERR_FILTER", "kind": "Gdef"}, "CAN_RAW_FD_FRAMES": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_FD_FRAMES", "kind": "Gdef"}, "CAN_RAW_FILTER": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_FILTER", "kind": "Gdef"}, "CAN_RAW_JOIN_FILTERS": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_JOIN_FILTERS", "kind": "Gdef"}, "CAN_RAW_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_LOOPBACK", "kind": "Gdef"}, "CAN_RAW_RECV_OWN_MSGS": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RAW_RECV_OWN_MSGS", "kind": "Gdef"}, "CAN_RTR_FLAG": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_RTR_FLAG", "kind": "Gdef"}, "CAN_SFF_MASK": {".class": "SymbolTableNode", "cross_ref": "_socket.CAN_SFF_MASK", "kind": "Gdef"}, "CAPI": {".class": "SymbolTableNode", "cross_ref": "_socket.CAPI", "kind": "Gdef"}, "CMSG_LEN": {".class": "SymbolTableNode", "cross_ref": "_socket.CMSG_LEN", "kind": "Gdef"}, "CMSG_SPACE": {".class": "SymbolTableNode", "cross_ref": "_socket.CMSG_SPACE", "kind": "Gdef"}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EAGAIN", "name": "EAGAIN", "setter_type": null, "type": "builtins.int"}}, "EAI_ADDRFAMILY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_ADDRFAMILY", "kind": "Gdef"}, "EAI_AGAIN": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_AGAIN", "kind": "Gdef"}, "EAI_BADFLAGS": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_BADFLAGS", "kind": "Gdef"}, "EAI_FAIL": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAIL", "kind": "Gdef"}, "EAI_FAMILY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAMILY", "kind": "Gdef"}, "EAI_MEMORY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_MEMORY", "kind": "Gdef"}, "EAI_NODATA": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NODATA", "kind": "Gdef"}, "EAI_NONAME": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NONAME", "kind": "Gdef"}, "EAI_OVERFLOW": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_OVERFLOW", "kind": "Gdef"}, "EAI_SERVICE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SERVICE", "kind": "Gdef"}, "EAI_SOCKTYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SOCKTYPE", "kind": "Gdef"}, "EAI_SYSTEM": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SYSTEM", "kind": "Gdef"}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EBADF", "name": "EBADF", "setter_type": null, "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EWOULDBLOCK", "name": "EWOULDBLOCK", "setter_type": null, "type": "builtins.int"}}, "INADDR_ALLHOSTS_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ALLHOSTS_GROUP", "kind": "Gdef"}, "INADDR_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ANY", "kind": "Gdef"}, "INADDR_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_BROADCAST", "kind": "Gdef"}, "INADDR_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_LOOPBACK", "kind": "Gdef"}, "INADDR_MAX_LOCAL_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_MAX_LOCAL_GROUP", "kind": "Gdef"}, "INADDR_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_NONE", "kind": "Gdef"}, "INADDR_UNSPEC_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_UNSPEC_GROUP", "kind": "Gdef"}, "IOBase": {".class": "SymbolTableNode", "cross_ref": "io.IOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IOCTL_VM_SOCKETS_GET_LOCAL_CID": {".class": "SymbolTableNode", "cross_ref": "_socket.IOCTL_VM_SOCKETS_GET_LOCAL_CID", "kind": "Gdef"}, "IPPORT_RESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_RESERVED", "kind": "Gdef"}, "IPPORT_USERRESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_USERRESERVED", "kind": "Gdef"}, "IPPROTO_AH": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_AH", "kind": "Gdef"}, "IPPROTO_DSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_DSTOPTS", "kind": "Gdef"}, "IPPROTO_EGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_EGP", "kind": "Gdef"}, "IPPROTO_ESP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ESP", "kind": "Gdef"}, "IPPROTO_FRAGMENT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_FRAGMENT", "kind": "Gdef"}, "IPPROTO_GRE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_GRE", "kind": "Gdef"}, "IPPROTO_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_HOPOPTS", "kind": "Gdef"}, "IPPROTO_ICMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMP", "kind": "Gdef"}, "IPPROTO_ICMPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMPV6", "kind": "Gdef"}, "IPPROTO_IDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IDP", "kind": "Gdef"}, "IPPROTO_IGMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IGMP", "kind": "Gdef"}, "IPPROTO_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IP", "kind": "Gdef"}, "IPPROTO_IPIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPIP", "kind": "Gdef"}, "IPPROTO_IPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPV6", "kind": "Gdef"}, "IPPROTO_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_NONE", "kind": "Gdef"}, "IPPROTO_PIM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PIM", "kind": "Gdef"}, "IPPROTO_PUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PUP", "kind": "Gdef"}, "IPPROTO_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RAW", "kind": "Gdef"}, "IPPROTO_ROUTING": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ROUTING", "kind": "Gdef"}, "IPPROTO_RSVP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RSVP", "kind": "Gdef"}, "IPPROTO_SCTP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_SCTP", "kind": "Gdef"}, "IPPROTO_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_TCP", "kind": "Gdef"}, "IPPROTO_TP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_TP", "kind": "Gdef"}, "IPPROTO_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_UDP", "kind": "Gdef"}, "IPPROTO_UDPLITE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_UDPLITE", "kind": "Gdef"}, "IPV6_CHECKSUM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_CHECKSUM", "kind": "Gdef"}, "IPV6_DONTFRAG": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_DONTFRAG", "kind": "Gdef"}, "IPV6_DSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_DSTOPTS", "kind": "Gdef"}, "IPV6_HOPLIMIT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPLIMIT", "kind": "Gdef"}, "IPV6_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPOPTS", "kind": "Gdef"}, "IPV6_JOIN_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_JOIN_GROUP", "kind": "Gdef"}, "IPV6_LEAVE_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_LEAVE_GROUP", "kind": "Gdef"}, "IPV6_MULTICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_HOPS", "kind": "Gdef"}, "IPV6_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_IF", "kind": "Gdef"}, "IPV6_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_LOOP", "kind": "Gdef"}, "IPV6_NEXTHOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_NEXTHOP", "kind": "Gdef"}, "IPV6_PATHMTU": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_PATHMTU", "kind": "Gdef"}, "IPV6_PKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_PKTINFO", "kind": "Gdef"}, "IPV6_RECVDSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVDSTOPTS", "kind": "Gdef"}, "IPV6_RECVHOPLIMIT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVHOPLIMIT", "kind": "Gdef"}, "IPV6_RECVHOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVHOPOPTS", "kind": "Gdef"}, "IPV6_RECVPATHMTU": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVPATHMTU", "kind": "Gdef"}, "IPV6_RECVPKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVPKTINFO", "kind": "Gdef"}, "IPV6_RECVRTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVRTHDR", "kind": "Gdef"}, "IPV6_RECVTCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVTCLASS", "kind": "Gdef"}, "IPV6_RTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDR", "kind": "Gdef"}, "IPV6_RTHDRDSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDRDSTOPTS", "kind": "Gdef"}, "IPV6_RTHDR_TYPE_0": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDR_TYPE_0", "kind": "Gdef"}, "IPV6_TCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_TCLASS", "kind": "Gdef"}, "IPV6_UNICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_UNICAST_HOPS", "kind": "Gdef"}, "IPV6_V6ONLY": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_V6ONLY", "kind": "Gdef"}, "IPX_TYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPX_TYPE", "kind": "Gdef", "module_public": false}, "IP_ADD_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_ADD_MEMBERSHIP", "kind": "Gdef"}, "IP_DEFAULT_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DEFAULT_MULTICAST_LOOP", "kind": "Gdef"}, "IP_DEFAULT_MULTICAST_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DEFAULT_MULTICAST_TTL", "kind": "Gdef"}, "IP_DROP_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DROP_MEMBERSHIP", "kind": "Gdef"}, "IP_HDRINCL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_HDRINCL", "kind": "Gdef"}, "IP_MAX_MEMBERSHIPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MAX_MEMBERSHIPS", "kind": "Gdef"}, "IP_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_IF", "kind": "Gdef"}, "IP_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_LOOP", "kind": "Gdef"}, "IP_MULTICAST_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_TTL", "kind": "Gdef"}, "IP_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_OPTIONS", "kind": "Gdef"}, "IP_RECVOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVOPTS", "kind": "Gdef"}, "IP_RECVRETOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVRETOPTS", "kind": "Gdef"}, "IP_RETOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RETOPTS", "kind": "Gdef"}, "IP_TOS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TOS", "kind": "Gdef"}, "IP_TRANSPARENT": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TRANSPARENT", "kind": "Gdef"}, "IP_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TTL", "kind": "Gdef"}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntFlag": {".class": "SymbolTableNode", "cross_ref": "enum.IntFlag", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "J1939_EE_INFO_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_EE_INFO_NONE", "kind": "Gdef"}, "J1939_EE_INFO_TX_ABORT": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_EE_INFO_TX_ABORT", "kind": "Gdef"}, "J1939_FILTER_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_FILTER_MAX", "kind": "Gdef"}, "J1939_IDLE_ADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_IDLE_ADDR", "kind": "Gdef"}, "J1939_MAX_UNICAST_ADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_MAX_UNICAST_ADDR", "kind": "Gdef"}, "J1939_NLA_BYTES_ACKED": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_NLA_BYTES_ACKED", "kind": "Gdef"}, "J1939_NLA_PAD": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_NLA_PAD", "kind": "Gdef"}, "J1939_NO_ADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_NO_ADDR", "kind": "Gdef"}, "J1939_NO_NAME": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_NO_NAME", "kind": "Gdef"}, "J1939_NO_PGN": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_NO_PGN", "kind": "Gdef"}, "J1939_PGN_ADDRESS_CLAIMED": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_PGN_ADDRESS_CLAIMED", "kind": "Gdef"}, "J1939_PGN_ADDRESS_COMMANDED": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_PGN_ADDRESS_COMMANDED", "kind": "Gdef"}, "J1939_PGN_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_PGN_MAX", "kind": "Gdef"}, "J1939_PGN_PDU1_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_PGN_PDU1_MAX", "kind": "Gdef"}, "J1939_PGN_REQUEST": {".class": "SymbolTableNode", "cross_ref": "_socket.J1939_PGN_REQUEST", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MSG_CMSG_CLOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_CMSG_CLOEXEC", "name": "MSG_CMSG_CLOEXEC", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_CONFIRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_CONFIRM", "name": "MSG_CONFIRM", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_CTRUNC", "name": "MSG_CTRUNC", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_DONTWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_DONTWAIT", "name": "MSG_DONTWAIT", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_EOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_EOR", "name": "MSG_EOR", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_ERRQUEUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_ERRQUEUE", "name": "MSG_ERRQUEUE", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_FASTOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_FASTOPEN", "name": "MSG_FASTOPEN", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_MORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_MORE", "name": "MSG_MORE", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_NOSIGNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_NOSIGNAL", "name": "MSG_NOSIGNAL", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_OOB", "name": "MSG_OOB", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_PEEK", "name": "MSG_PEEK", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_TRUNC", "name": "MSG_TRUNC", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_WAITALL", "name": "MSG_WAITALL", "setter_type": null, "type": "socket.MsgFlag"}}, "MsgFlag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.MsgFlag", "name": "MsgFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.MsgFlag", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.MsgFlag", "enum.IntFlag", "builtins.int", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MSG_CMSG_CLOEXEC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_CMSG_CLOEXEC", "name": "MSG_CMSG_CLOEXEC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1073741821}, "type_ref": "builtins.int"}}}, "MSG_CONFIRM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_CONFIRM", "name": "MSG_CONFIRM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2048}, "type_ref": "builtins.int"}}}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_CTRUNC", "name": "MSG_CTRUNC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "MSG_DONTWAIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_DONTWAIT", "name": "MSG_DONTWAIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "MSG_EOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_EOR", "name": "MSG_EOR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "MSG_ERRQUEUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_ERRQUEUE", "name": "MSG_ERRQUEUE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}, "type_ref": "builtins.int"}}}, "MSG_FASTOPEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_FASTOPEN", "name": "MSG_FASTOPEN", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 536870912}, "type_ref": "builtins.int"}}}, "MSG_MORE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_MORE", "name": "MSG_MORE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32768}, "type_ref": "builtins.int"}}}, "MSG_NOSIGNAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_NOSIGNAL", "name": "MSG_NOSIGNAL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_OOB", "name": "MSG_OOB", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_PEEK", "name": "MSG_PEEK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_TRUNC", "name": "MSG_TRUNC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_WAITALL", "name": "MSG_WAITALL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.MsgFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.MsgFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NETLINK_CRYPTO": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_CRYPTO", "kind": "Gdef"}, "NETLINK_DNRTMSG": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_DNRTMSG", "kind": "Gdef"}, "NETLINK_FIREWALL": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_FIREWALL", "kind": "Gdef"}, "NETLINK_IP6_FW": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_IP6_FW", "kind": "Gdef"}, "NETLINK_NFLOG": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_NFLOG", "kind": "Gdef"}, "NETLINK_ROUTE": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_ROUTE", "kind": "Gdef"}, "NETLINK_USERSOCK": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_USERSOCK", "kind": "Gdef"}, "NETLINK_XFRM": {".class": "SymbolTableNode", "cross_ref": "_socket.NETLINK_XFRM", "kind": "Gdef"}, "NI_DGRAM": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_DGRAM", "kind": "Gdef"}, "NI_MAXHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXHOST", "kind": "Gdef"}, "NI_MAXSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXSERV", "kind": "Gdef"}, "NI_NAMEREQD": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NAMEREQD", "kind": "Gdef"}, "NI_NOFQDN": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NOFQDN", "kind": "Gdef"}, "NI_NUMERICHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICHOST", "kind": "Gdef"}, "NI_NUMERICSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICSERV", "kind": "Gdef"}, "PACKET_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_BROADCAST", "kind": "Gdef"}, "PACKET_FASTROUTE": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_FASTROUTE", "kind": "Gdef"}, "PACKET_HOST": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_HOST", "kind": "Gdef"}, "PACKET_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_LOOPBACK", "kind": "Gdef"}, "PACKET_MULTICAST": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_MULTICAST", "kind": "Gdef"}, "PACKET_OTHERHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_OTHERHOST", "kind": "Gdef"}, "PACKET_OUTGOING": {".class": "SymbolTableNode", "cross_ref": "_socket.PACKET_OUTGOING", "kind": "Gdef"}, "PF_CAN": {".class": "SymbolTableNode", "cross_ref": "_socket.PF_CAN", "kind": "Gdef"}, "PF_PACKET": {".class": "SymbolTableNode", "cross_ref": "_socket.PF_PACKET", "kind": "Gdef"}, "PF_RDS": {".class": "SymbolTableNode", "cross_ref": "_socket.PF_RDS", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RDS_CANCEL_SENT_TO": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CANCEL_SENT_TO", "kind": "Gdef", "module_public": false}, "RDS_CMSG_RDMA_ARGS": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CMSG_RDMA_ARGS", "kind": "Gdef", "module_public": false}, "RDS_CMSG_RDMA_DEST": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CMSG_RDMA_DEST", "kind": "Gdef", "module_public": false}, "RDS_CMSG_RDMA_MAP": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CMSG_RDMA_MAP", "kind": "Gdef", "module_public": false}, "RDS_CMSG_RDMA_STATUS": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CMSG_RDMA_STATUS", "kind": "Gdef", "module_public": false}, "RDS_CONG_MONITOR": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_CONG_MONITOR", "kind": "Gdef", "module_public": false}, "RDS_FREE_MR": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_FREE_MR", "kind": "Gdef", "module_public": false}, "RDS_GET_MR": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_GET_MR", "kind": "Gdef", "module_public": false}, "RDS_GET_MR_FOR_DEST": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_GET_MR_FOR_DEST", "kind": "Gdef", "module_public": false}, "RDS_RDMA_DONTWAIT": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_DONTWAIT", "kind": "Gdef", "module_public": false}, "RDS_RDMA_FENCE": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_FENCE", "kind": "Gdef", "module_public": false}, "RDS_RDMA_INVALIDATE": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_INVALIDATE", "kind": "Gdef", "module_public": false}, "RDS_RDMA_NOTIFY_ME": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_NOTIFY_ME", "kind": "Gdef", "module_public": false}, "RDS_RDMA_READWRITE": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_READWRITE", "kind": "Gdef", "module_public": false}, "RDS_RDMA_SILENT": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_SILENT", "kind": "Gdef", "module_public": false}, "RDS_RDMA_USE_ONCE": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RDMA_USE_ONCE", "kind": "Gdef", "module_public": false}, "RDS_RECVERR": {".class": "SymbolTableNode", "cross_ref": "_socket.RDS_RECVERR", "kind": "Gdef", "module_public": false}, "RawIOBase": {".class": "SymbolTableNode", "cross_ref": "io.RawIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SCM_CREDENTIALS": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_CREDENTIALS", "kind": "Gdef"}, "SCM_J1939_DEST_ADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_J1939_DEST_ADDR", "kind": "Gdef"}, "SCM_J1939_DEST_NAME": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_J1939_DEST_NAME", "kind": "Gdef"}, "SCM_J1939_ERRQUEUE": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_J1939_ERRQUEUE", "kind": "Gdef"}, "SCM_J1939_PRIO": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_J1939_PRIO", "kind": "Gdef"}, "SCM_RIGHTS": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_RIGHTS", "kind": "Gdef"}, "SHUT_RD": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RD", "kind": "Gdef"}, "SHUT_RDWR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RDWR", "kind": "Gdef"}, "SHUT_WR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_WR", "kind": "Gdef"}, "SOCK_CLOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_CLOEXEC", "name": "SOCK_CLOEXEC", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_DGRAM", "name": "SOCK_DGRAM", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_NONBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_NONBLOCK", "name": "SOCK_NONBLOCK", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RAW", "name": "SOCK_RAW", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RDM", "name": "SOCK_RDM", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_STREAM", "name": "SOCK_STREAM", "setter_type": null, "type": "socket.SocketKind"}}, "SOL_ALG": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_ALG", "kind": "Gdef"}, "SOL_ATALK": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_ATALK", "kind": "Gdef", "module_public": false}, "SOL_AX25": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_AX25", "kind": "Gdef", "module_public": false}, "SOL_CAN_BASE": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_CAN_BASE", "kind": "Gdef"}, "SOL_CAN_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_CAN_RAW", "kind": "Gdef"}, "SOL_HCI": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_HCI", "kind": "Gdef", "module_public": false}, "SOL_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_IP", "kind": "Gdef"}, "SOL_IPX": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_IPX", "kind": "Gdef", "module_public": false}, "SOL_NETROM": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_NETROM", "kind": "Gdef", "module_public": false}, "SOL_RDS": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_RDS", "kind": "Gdef"}, "SOL_ROSE": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_ROSE", "kind": "Gdef", "module_public": false}, "SOL_SOCKET": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_SOCKET", "kind": "Gdef"}, "SOL_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_TCP", "kind": "Gdef"}, "SOL_TIPC": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_TIPC", "kind": "Gdef"}, "SOL_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_UDP", "kind": "Gdef"}, "SOMAXCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SOMAXCONN", "kind": "Gdef"}, "SO_ACCEPTCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ACCEPTCONN", "kind": "Gdef"}, "SO_BINDTODEVICE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_BINDTODEVICE", "kind": "Gdef"}, "SO_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_BROADCAST", "kind": "Gdef"}, "SO_DEBUG": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DEBUG", "kind": "Gdef"}, "SO_DOMAIN": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DOMAIN", "kind": "Gdef"}, "SO_DONTROUTE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DONTROUTE", "kind": "Gdef"}, "SO_ERROR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ERROR", "kind": "Gdef"}, "SO_J1939_ERRQUEUE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_J1939_ERRQUEUE", "kind": "Gdef"}, "SO_J1939_FILTER": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_J1939_FILTER", "kind": "Gdef"}, "SO_J1939_PROMISC": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_J1939_PROMISC", "kind": "Gdef"}, "SO_J1939_SEND_PRIO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_J1939_SEND_PRIO", "kind": "Gdef"}, "SO_KEEPALIVE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_KEEPALIVE", "kind": "Gdef"}, "SO_LINGER": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_LINGER", "kind": "Gdef"}, "SO_MARK": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_MARK", "kind": "Gdef"}, "SO_OOBINLINE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_OOBINLINE", "kind": "Gdef"}, "SO_PASSCRED": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PASSCRED", "kind": "Gdef"}, "SO_PASSSEC": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PASSSEC", "kind": "Gdef"}, "SO_PEERCRED": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PEERCRED", "kind": "Gdef"}, "SO_PEERSEC": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PEERSEC", "kind": "Gdef"}, "SO_PRIORITY": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PRIORITY", "kind": "Gdef"}, "SO_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_PROTOCOL", "kind": "Gdef"}, "SO_RCVBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVBUF", "kind": "Gdef"}, "SO_RCVLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVLOWAT", "kind": "Gdef"}, "SO_RCVTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVTIMEO", "kind": "Gdef"}, "SO_REUSEADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_REUSEADDR", "kind": "Gdef"}, "SO_REUSEPORT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_REUSEPORT", "kind": "Gdef"}, "SO_SNDBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDBUF", "kind": "Gdef"}, "SO_SNDLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDLOWAT", "kind": "Gdef"}, "SO_SNDTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDTIMEO", "kind": "Gdef"}, "SO_TYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_TYPE", "kind": "Gdef"}, "SO_VM_SOCKETS_BUFFER_MAX_SIZE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_VM_SOCKETS_BUFFER_MAX_SIZE", "kind": "Gdef"}, "SO_VM_SOCKETS_BUFFER_MIN_SIZE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_VM_SOCKETS_BUFFER_MIN_SIZE", "kind": "Gdef"}, "SO_VM_SOCKETS_BUFFER_SIZE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_VM_SOCKETS_BUFFER_SIZE", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SocketIO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.RawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketIO", "name": "SocketIO", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.SocketIO", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketIO", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "arg_types": ["socket.SocketIO", "socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SocketIO", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.SocketIO.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.mode", "name": "mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.SocketIO.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readinto of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketKind", "name": "SocketKind", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.SocketKind", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketKind", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SOCK_CLOEXEC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_CLOEXEC", "name": "SOCK_CLOEXEC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 524288}, "type_ref": "builtins.int"}}}, "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_DGRAM", "name": "SOCK_DGRAM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SOCK_NONBLOCK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_NONBLOCK", "name": "SOCK_NONBLOCK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2048}, "type_ref": "builtins.int"}}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RAW", "name": "SOCK_RAW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RDM", "name": "SOCK_RDM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_STREAM", "name": "SOCK_STREAM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketKind.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketKind", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketType": {".class": "SymbolTableNode", "cross_ref": "_socket.SocketType", "kind": "Gdef"}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TCP_CONGESTION": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_CONGESTION", "kind": "Gdef"}, "TCP_CORK": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_CORK", "kind": "Gdef"}, "TCP_DEFER_ACCEPT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_DEFER_ACCEPT", "kind": "Gdef"}, "TCP_FASTOPEN": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_FASTOPEN", "kind": "Gdef"}, "TCP_INFO": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_INFO", "kind": "Gdef"}, "TCP_KEEPCNT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPCNT", "kind": "Gdef"}, "TCP_KEEPIDLE": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPIDLE", "kind": "Gdef"}, "TCP_KEEPINTVL": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPINTVL", "kind": "Gdef"}, "TCP_LINGER2": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_LINGER2", "kind": "Gdef"}, "TCP_MAXSEG": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_MAXSEG", "kind": "Gdef"}, "TCP_NODELAY": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_NODELAY", "kind": "Gdef"}, "TCP_NOTSENT_LOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_NOTSENT_LOWAT", "kind": "Gdef"}, "TCP_QUICKACK": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_QUICKACK", "kind": "Gdef"}, "TCP_SYNCNT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_SYNCNT", "kind": "Gdef"}, "TCP_USER_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_USER_TIMEOUT", "kind": "Gdef"}, "TCP_WINDOW_CLAMP": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_WINDOW_CLAMP", "kind": "Gdef"}, "TIPC_ADDR_ID": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_ADDR_ID", "kind": "Gdef"}, "TIPC_ADDR_NAME": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_ADDR_NAME", "kind": "Gdef"}, "TIPC_ADDR_NAMESEQ": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_ADDR_NAMESEQ", "kind": "Gdef"}, "TIPC_CFG_SRV": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_CFG_SRV", "kind": "Gdef"}, "TIPC_CLUSTER_SCOPE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_CLUSTER_SCOPE", "kind": "Gdef"}, "TIPC_CONN_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_CONN_TIMEOUT", "kind": "Gdef"}, "TIPC_CRITICAL_IMPORTANCE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_CRITICAL_IMPORTANCE", "kind": "Gdef"}, "TIPC_DEST_DROPPABLE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_DEST_DROPPABLE", "kind": "Gdef"}, "TIPC_HIGH_IMPORTANCE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_HIGH_IMPORTANCE", "kind": "Gdef"}, "TIPC_IMPORTANCE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_IMPORTANCE", "kind": "Gdef"}, "TIPC_LOW_IMPORTANCE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_LOW_IMPORTANCE", "kind": "Gdef"}, "TIPC_MEDIUM_IMPORTANCE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_MEDIUM_IMPORTANCE", "kind": "Gdef"}, "TIPC_NODE_SCOPE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_NODE_SCOPE", "kind": "Gdef"}, "TIPC_PUBLISHED": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_PUBLISHED", "kind": "Gdef"}, "TIPC_SRC_DROPPABLE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_SRC_DROPPABLE", "kind": "Gdef"}, "TIPC_SUBSCR_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_SUBSCR_TIMEOUT", "kind": "Gdef"}, "TIPC_SUB_CANCEL": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_SUB_CANCEL", "kind": "Gdef"}, "TIPC_SUB_PORTS": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_SUB_PORTS", "kind": "Gdef"}, "TIPC_SUB_SERVICE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_SUB_SERVICE", "kind": "Gdef"}, "TIPC_TOP_SRV": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_TOP_SRV", "kind": "Gdef"}, "TIPC_WAIT_FOREVER": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_WAIT_FOREVER", "kind": "Gdef"}, "TIPC_WITHDRAWN": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_WITHDRAWN", "kind": "Gdef"}, "TIPC_ZONE_SCOPE": {".class": "SymbolTableNode", "cross_ref": "_socket.TIPC_ZONE_SCOPE", "kind": "Gdef"}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UDPLITE_RECV_CSCOV": {".class": "SymbolTableNode", "cross_ref": "_socket.UDPLITE_RECV_CSCOV", "kind": "Gdef"}, "UDPLITE_SEND_CSCOV": {".class": "SymbolTableNode", "cross_ref": "_socket.UDPLITE_SEND_CSCOV", "kind": "Gdef"}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VMADDR_CID_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.VMADDR_CID_ANY", "kind": "Gdef"}, "VMADDR_CID_HOST": {".class": "SymbolTableNode", "cross_ref": "_socket.VMADDR_CID_HOST", "kind": "Gdef"}, "VMADDR_PORT_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.VMADDR_PORT_ANY", "kind": "Gdef"}, "VM_SOCKETS_INVALID_VERSION": {".class": "SymbolTableNode", "cross_ref": "_socket.VM_SOCKETS_INVALID_VERSION", "kind": "Gdef"}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Address": {".class": "SymbolTableNode", "cross_ref": "_socket._Address", "kind": "Gdef", "module_public": false}, "_RetAddress": {".class": "SymbolTableNode", "cross_ref": "_socket._RetAddress", "kind": "Gdef", "module_public": false}, "_SendableFile": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket._SendableFile", "name": "_SendableFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "socket._SendableFile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket._SendableFile", "builtins.object"], "names": {".class": "SymbolTable", "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket._SendableFile.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of _SendableFile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket._SendableFile.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "seek of _SendableFile", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket._SendableFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket._SendableFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_socket": {".class": "SymbolTableNode", "cross_ref": "_socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "close": {".class": "SymbolTableNode", "cross_ref": "_socket.close", "kind": "Gdef"}, "create_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["address", "timeout", "source_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["address", "timeout", "source_address"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_connection", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_server", "name": "create_server", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_server", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "cross_ref": "_socket.dup", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "socket.error", "line": 1066, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "fromfd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.fromfd", "name": "fromfd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "arg_types": ["typing.SupportsIndex", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fromfd", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gaierror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.gaierror", "name": "gaier<PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.gaierror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.gaierror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.gaierror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.gaierror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getaddrinfo", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.getdefaulttimeout", "kind": "Gdef"}, "getfqdn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getfqdn", "name": "getfqdn", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getfqdn", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyaddr", "kind": "Gdef"}, "gethostbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname", "kind": "Gdef"}, "gethostbyname_ex": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname_ex", "kind": "Gdef"}, "gethostname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostname", "kind": "Gdef"}, "getnameinfo": {".class": "SymbolTableNode", "cross_ref": "_socket.getnameinfo", "kind": "Gdef"}, "getprotobyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getprotobyname", "kind": "Gdef"}, "getservbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyname", "kind": "Gdef"}, "getservbyport": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyport", "kind": "Gdef"}, "has_dualstack_ipv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.has_dualstack_ipv6", "name": "has_dualstack_ipv6", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_dualstack_ipv6", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_ipv6": {".class": "SymbolTableNode", "cross_ref": "_socket.has_ipv6", "kind": "Gdef"}, "herror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.herror", "name": "herror", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.herror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.herror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.herror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.herror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "htonl": {".class": "SymbolTableNode", "cross_ref": "_socket.htonl", "kind": "Gdef"}, "htons": {".class": "SymbolTableNode", "cross_ref": "_socket.htons", "kind": "Gdef"}, "if_indextoname": {".class": "SymbolTableNode", "cross_ref": "_socket.if_indextoname", "kind": "Gdef"}, "if_nameindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nameindex", "kind": "Gdef"}, "if_nametoindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nametoindex", "kind": "Gdef"}, "inet_aton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_aton", "kind": "Gdef"}, "inet_ntoa": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntoa", "kind": "Gdef"}, "inet_ntop": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntop", "kind": "Gdef"}, "inet_pton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_pton", "kind": "Gdef"}, "ntohl": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohl", "kind": "Gdef"}, "ntohs": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohs", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "recv_fds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "bufsize", "maxfds", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.recv_fds", "name": "recv_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "bufsize", "maxfds", "flags"], "arg_types": ["socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv_fds", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_fds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["sock", "buffers", "fds", "flags", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.send_fds", "name": "send_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["sock", "buffers", "fds", "flags", "address"], "arg_types": ["socket.socket", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.object", "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_fds", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.setdefaulttimeout", "kind": "Gdef"}, "sethostname": {".class": "SymbolTableNode", "cross_ref": "_socket.sethostname", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_socket.socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.socket", "name": "socket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.socket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.socket", "_socket.socket", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["socket.socket", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accept of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.dup", "name": "dup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dup of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "family": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.family", "name": "family", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.family", "name": "family", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.get_inheritable", "name": "get_inheritable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_inheritable of socket", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "socket.socket.makefile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sendfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.sendfile", "name": "sendfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "arg_types": ["socket.socket", "socket._SendableFile", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendfile of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.set_inheritable", "name": "set_inheritable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "arg_types": ["socket.socket", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_inheritable of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.type", "name": "type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "socketpair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socketpair", "name": "socketpair", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "socket.AddressFamily", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_socket.socket", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "socketpair", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", "socket.socket"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.timeout", "name": "timeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.timeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.timeout", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.timeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.timeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "/home/<USER>/miniconda3/envs/exp/lib/python3.9/site-packages/mypy/typeshed/stdlib/socket.pyi"}